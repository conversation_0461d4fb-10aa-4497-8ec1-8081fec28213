package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.ShipmentApi;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.service.IShipmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ShipmentApiController implements ShipmentApi {

    private final IShipmentService shipmentService;

    @Override
    public ResponseEntity<ListResponse<ShipmentListingResponse>> listShipments(ShipmentListingRequest shipmentListingRequest) {
        return ResponseEntity.ok(shipmentService.listShipments(shipmentListingRequest));
    }
}
