package com.dpw.ctms.move.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "exception_task_mapping")
@EqualsAndHashCode(callSuper = true)
@Audited
public class ExceptionTaskMapping extends BaseEntity {

    @OneToOne
    @JoinColumn(name = "exception_id")
    private Exception exception;

    @ManyToOne
    @JoinColumn(name = "task_id")
    private Task task;
}