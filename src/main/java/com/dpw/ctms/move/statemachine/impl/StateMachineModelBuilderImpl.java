package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.dto.StateConfigDTO;
import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.dto.TransitionConfigDTO;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.IStateMachineModelBuilder;
import com.dpw.ctms.move.statemachine.registry.IGuardRegistry;
import com.dpw.ctms.move.statemachine.registry.StateMachineConfigFetcherRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.config.model.*;
import org.springframework.statemachine.guard.Guard;
import org.springframework.statemachine.transition.TransitionKind;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Service
@RequiredArgsConstructor
@Slf4j
public class StateMachineModelBuilderImpl implements IStateMachineModelBuilder {

    private final StateMachineConfigFetcherRegistry stateMachineConfigFetcherRegistry;
    private final Map<StateMachineEntityType, IGuardRegistry> guardRegistryMap;

    @Override
    public StateMachineModel<String, String> build(String tenantId, StateMachineEntityType entityType) {
        StateTransitionHolderDTO stateTransitionHolderDTO = stateMachineConfigFetcherRegistry.getStateMachineConfig(entityType, tenantId);
        IGuardRegistry guardRegistry = getGuardRegistry(entityType);
        List<StateConfigDTO> stateConfigDTOArrayList = stateTransitionHolderDTO.getStates();
        List<StateData<String, String>> stateDataList = stateConfigDTOArrayList.stream().map(stateConfigDTO ->
                new StateData<String, String>(stateConfigDTO.getState(), stateConfigDTO.getIsInitial())
        ).toList();

        List<TransitionConfigDTO> transitionConfigDTOArrayList = stateTransitionHolderDTO.getTransitions();
        List<TransitionData<String, String>> transitionDataList = transitionConfigDTOArrayList.stream().map(transitionConfigDTO -> {
                    Guard<String,String> guard = (guardRegistry != null && !transitionConfigDTO.getGuardId().isEmpty()) ?
                            guardRegistry.getGuard(transitionConfigDTO.getGuardId()) : null;

                    Function<StateContext<String, String>, Mono<Boolean>> guardFunction =
                            (guard != null)
                                    ? context -> Mono.just(guard.evaluate(context))
                                    : null;

                    return new TransitionData<>(
                            transitionConfigDTO.getSourceState(),
                            transitionConfigDTO.getTargetState(),
                            transitionConfigDTO.getEvent(),
                            null,
                            guardFunction,
                            TransitionKind.EXTERNAL
                    );
                }
        ).toList();

        return new DefaultStateMachineModel<>(new ConfigurationData<>(),
                new StatesData<>(stateDataList),
                new TransitionsData<>(transitionDataList));
    }


    private IGuardRegistry getGuardRegistry(StateMachineEntityType entityType) {
        return guardRegistryMap.get(entityType);
    }
}
