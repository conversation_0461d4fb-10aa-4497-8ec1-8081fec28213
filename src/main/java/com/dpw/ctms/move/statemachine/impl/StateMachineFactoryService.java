package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.IStateMachineModelBuilder;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.ObjectStateMachineFactory;
import org.springframework.statemachine.config.model.StateMachineModel;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.ERROR_CREATING_STATE_MACHINE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INTERNAL_ERROR;

@Service
@RequiredArgsConstructor
@Slf4j
public class StateMachineFactoryService {

    private final IStateMachineModelBuilder modelBuilder;

    public StateMachine<String, String> createStateMachine(String tenantId, StateMachineEntityType entityType) {
        try {
            StateMachineModel<String, String> model = modelBuilder.build(tenantId, entityType);
            ObjectStateMachineFactory<String, String> factory = new ObjectStateMachineFactory<>(model);
            return factory.getStateMachine();
        } catch (Exception exception) {
            log.error("Exception occurred while creating state machine: {}", exception.getMessage());
            throw new TMSException(INTERNAL_ERROR.name(), String.format(ERROR_CREATING_STATE_MACHINE, exception));
        }
    }
}

