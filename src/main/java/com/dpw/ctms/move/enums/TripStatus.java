package com.dpw.ctms.move.enums;

public enum TripStatus implements DisplayableStatusEnum {
    DRAFT("Draft"),
    PLANNED("Planned"),
    ACTIVE("Active"),
    DISCARDED("Discarded"),
    IN_TRANSIT("In Transit"),
    COMPLETED("Completed"),
    CANCELLED("Cancelled"),
    CLOSED("Closed"),
    CONFIRMATION_WAITING("Confirmation Waiting"),
    AMENDMENT_INPROGRESS("Amendment In Progress"),
    ENROUTE("Enroute");

    private final String displayText;

    TripStatus(String displayText) {
        this.displayText = displayText;
    }

    public String getDisplayName() {
        return this.displayText;
    }

    public static TripStatus fromDisplayName(String displayText) {
        if (displayText == null) {
            return null;
        }
        for (TripStatus status : TripStatus.values()) {
            if (status.displayText.equalsIgnoreCase(displayText)) {
                return status;
            }
        }
        return null;
    }

}
