package com.dpw.ctms.move.enums;

public enum AssignmentType implements DisplayableStatusEnum{
    INTERNAL("Internal"),
    EXTERNAL("External");

    private final String displayName; // Made final

    AssignmentType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public static AssignmentType fromDisplayName(String displayName) {
        for (AssignmentType type : AssignmentType.values()) {
            if (type.displayName.equalsIgnoreCase(displayName)) {
                return type;
            }
        }
        return null;
    }
}
