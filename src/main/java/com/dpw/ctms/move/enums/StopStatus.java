package com.dpw.ctms.move.enums;

public enum StopStatus implements DisplayableStatusEnum {
    PLANNED("Planned"),
    COMPLETED("Completed"),
    CANCELLED("Cancelled"),
    AMENDMENT_INPROGRESS("Amendment In Progress"),
    DISCARDED("Discarded");

    private final String displayText;

    StopStatus(String displayText) {
        this.displayText = displayText;
    }

    @Override
    public String getDisplayName() {
        return this.displayText;
    }

}