package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.AttachmentType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

import java.util.UUID;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "attachment")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Attachment extends BaseEntity {

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private AttachmentType type;

    @Column(name = "external_file_identifier", unique = true)
    private UUID externalFileIdentifier;

    @Column(name = "size")
    private Integer size;

    @Column(name = "name")
    private String name;

    @Column(name = "path")
    private String path;
}