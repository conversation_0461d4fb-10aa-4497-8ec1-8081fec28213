package com.dpw.ctms.move;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@SpringBootApplication
@EnableJpaAuditing
@ComponentScan(basePackages = {"com.dpw"})
@EntityScan("com.dpw")
@EnableFeignClients(basePackages = "com.dpw")
@ServletComponentScan(basePackages = "com.dpw")
@EnableJpaRepositories(basePackages = "com.dpw")
@EnableAsync
@EnableAspectJAutoProxy
@EnableScheduling
public class MoveApplication {

    @Value("${tms.app.timezone:UTC}")
    private String timezone;

    public static void main(String[] args) {
        SpringApplication.run(MoveApplication.class, args);
    }

    @PostConstruct
    public void init() {
        TimeZone.setDefault(TimeZone.getTimeZone(timezone));
    }

}