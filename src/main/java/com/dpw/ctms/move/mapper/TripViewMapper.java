package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.TripViewResponse;
import org.mapstruct.*;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TripViewMapper {

    @Mapping(target = "code", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapEnumLabelResponse")
    @Mapping(target = "resourceDetails", source = ".")
    @Mapping(target = "stops", source = "stops")
    @Mapping(target = "shipments", source = "shipments")
    @Mapping(target = "transportOrder", source = "transportOrder")
    TripViewResponse toResponse(Trip trip);

    @Mapping(target = "vehicleResource", source = "vehicleResource")
    @Mapping(target = "trailerResources", source = "trailerResources")
    @Mapping(target = "vehicleOperatorResources", source = "vehicleOperatorResources")
    TripViewResponse.ResourceDetails toResourceDetails(Trip trip);

    @Mapping(target = "externalResourceId", source = "externalResourceId")
    @Mapping(target = "resourceAssignmentType", source = "resourceAssignmentDetails.resourceAssignmentType", qualifiedByName = "mapEnumLabelResponse")
    TripViewResponse.ResourceDetails.VehicleResource toVehicleResource(VehicleResource vehicleResource);

    @Mapping(target = "externalResourceId", source = "externalResourceId")
    TripViewResponse.ResourceDetails.ExternalResource toTrailerResource(TrailerResource trailerResource);

    @Mapping(target = "externalResourceId", source = "externalResourceId")
    TripViewResponse.ResourceDetails.ExternalResource toVehicleOperatorResource(VehicleOperatorResource vehicleOperatorResource);

    @Mapping(target = "code", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapEnumLabelResponse")
    @Mapping(target = "expectedPickupAt", source = "expectedPickupAt")
    @Mapping(target = "expectedDeliveryAt", source = "expectedDeliveryAt")
    @Mapping(target = "actualPickupAt", source = "actualPickupAt")
    @Mapping(target = "actualDeliveryAt", source = "actualDeliveryAt")
    @Mapping(target = "externalConsignmentId", source = "externalConsignmentId")
    @Mapping(target = "externalCustomerOrderId", source = "externalCustomerOrderId")
    @Mapping(target = "volume", source = "volume")
    @Mapping(target = "volumeUom", source = "volumeUom")
    @Mapping(target = "weight", source = "weight")
    @Mapping(target = "weightUom", source = "weightUom")
    @Mapping(target = "originStopCode", source = "originStop.code")
    @Mapping(target = "destinationStopCode", source = "destinationStop.code")
    TripViewResponse.ShipmentDetails toShipmentDetails(Shipment shipment);

    @Mapping(target = "code", source = "code")
    @Mapping(target = "externalLocationCode", source = "externalLocationCode")
    TripViewResponse.StopDetails toStopDetails(Stop stop);

    @Mapping(target = "code", source = "code")
    @Mapping(target = "id", source = "id")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapEnumLabelResponse")
    TripViewResponse.TransportOrderDetails toTransportOrderDetails(TransportOrder transportOrder);

    @Named("mapEnumLabelResponse")
    default EnumLabelValueResponse mapEnumLabelResponse(DisplayableStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return new EnumLabelValueResponse(statusEnum.getDisplayName(), statusEnum.name());
    }

}
