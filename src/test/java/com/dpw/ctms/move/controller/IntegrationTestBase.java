package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.testcontainers.SharedPostgreSQLContainer;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public abstract class IntegrationTestBase {

    /**
     * Ensures the shared container is ready and cfr schema exists before any tests run.
     */
    @BeforeAll
    static void setupSharedDatabase() {
        // Ensure the shared container is running
        SharedPostgreSQLContainer.getInstance();

        // Ensure the cfr schema exists for multi-tenant tests
        TestDatabaseManager.ensureCfrSchemaExists();
    }

    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        // Use the shared container for all integration tests
        registry.add("spring.datasource.url", SharedPostgreSQLContainer::getJdbcUrl);
        registry.add("spring.datasource.username", SharedPostgreSQLContainer::getUsername);
        registry.add("spring.datasource.password", SharedPostgreSQLContainer::getPassword);
        registry.add("spring.datasource.hikari.maximum-pool-size", () -> "5");
        registry.add("spring.datasource.hikari.connection-timeout", () -> "30000");
        registry.add("spring.datasource.hikari.validation-timeout", () -> "5000");
        registry.add("spring.datasource.hikari.idle-timeout", () -> "300000");
    }

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected ObjectMapper objectMapper;
}