package com.dpw.ctms.move.dto.consumer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class PickupTaskMessageDTO {
    private ShipmentMessageDTO shipment;
    private TaskDetailDTO taskDetail;
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class ShipmentMessageDTO {
        private String startTime;
        private String loadingTime;
        private String departureTime;

    }
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class TaskDetailDTO {
        private String taskName;
    }
}