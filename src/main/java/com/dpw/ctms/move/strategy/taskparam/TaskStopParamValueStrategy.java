package com.dpw.ctms.move.strategy.taskparam;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.dto.ParamValueStopDTO;
import com.dpw.ctms.move.enums.TaskParamType;
import org.springframework.stereotype.Component;

@Component
public class TaskStopParamValueStrategy implements ITaskParamValueStrategy{
    @Override
    public TaskParamType getParamType() {
        return TaskParamType.STOP;
    }

    @Override
    public Class<? extends ParamValueBaseDTO>  getParamValue() {
        return ParamValueStopDTO.class;
    }
}
