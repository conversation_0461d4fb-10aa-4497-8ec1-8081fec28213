package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.AssignmentStatus;
import com.dpw.ctms.move.enums.ResourceAssignmentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResourceAssignmentDetails {
    private AssignmentStatus assignmentStatus;
    private ResourceAssignmentType resourceAssignmentType;
    private Long assignedAt;
    private String assignedBy;
}
