package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.LocationDetail;
import com.dpw.ctms.move.dto.TripTasksDetailsDTO;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.mapper.TaskMapper;
import com.dpw.ctms.move.request.FilterRequest;
import com.dpw.ctms.move.request.TripTaskListingRequest;
import com.dpw.ctms.move.response.*;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.service.TripStopTaskService;
import com.dpw.tmsutils.annotation.MethodLog;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@RequiredArgsConstructor
public class TripStopTaskServiceImpl implements TripStopTaskService {
    private final ITripDataService tripDataService;
    private final TaskMapper taskMapper;

    @Transactional(value = "tripsTransactionManager")
    @MethodLog
    @Override
    public ListResponse<TripFacilityTasksDetailsResponse> getTripTasks(
            String tripCode, FilterRequest<TripTaskListingRequest> tripTaskListingDTO) {
        Page<Stop> tripStopPage = tripDataService.getTripStopByTripCode(tripCode, tripTaskListingDTO.getPagination());
        List<Stop> tripStopList = tripStopPage.stream().sorted(Comparator.comparing(Stop::getSequence, Comparator.nullsFirst(Comparator.naturalOrder())))
                .toList();

        Long totalCount = tripStopPage.getTotalElements();

        List<TripFacilityTasksDetailsResponse> list = new ArrayList<>();
        for(Stop stop: tripStopList) {
            List<TripTasksDetailsDTO> tripTasksDetailsResponses = new ArrayList<>();

            for(StopTask stopTask : stop.getTripStopActiveTasks()){
                tripTasksDetailsResponses.add(taskMapper.toDTO(stopTask.getTask()));
            }
            var tripFacilityTasksDetails = TripFacilityTasksDetailsResponse.builder().locationDetails(LocationDetail.builder()
                    .externalLocationCode(stop.getExternalLocationCode())
                            .sequence(stop.getSequence())
                            .code(stop.getCode())
                            .updatedAt(stop.getUpdatedAt())
                            .build())
                    .locationTaskDetailsList(tripTasksDetailsResponses).build();
            list.add(tripFacilityTasksDetails);
        }
        return ListResponse.<TripFacilityTasksDetailsResponse>builder()
                .data(list)
                .totalRecords(totalCount)
                .build();
    }
}
