#!/bin/bash
Branch_Name=$1
current_release_version=$2

#Checking if branch name is having valid release version
if [[ ${Branch_Name} =~ ^Release-v[0-9][0-9]?\.[0-9][0-9]?\.[0-9][0-9]?$ ]] \
&& [[ ! `git branch -r | grep -w ${Branch_Name}` ]]; then
  major_version_new=`echo ${Branch_Name} | cut -d"-" -f2 | cut -d"." -f1 | cut -c2`
  minor_version_new=`echo ${Branch_Name} | cut -d"-" -f2 | cut -d"." -f2`
  hf_version_new=`echo ${Branch_Name} | cut -d"-" -f2 | cut -d"." -f3`

  #Checking branch version if its a HF
  if [[ "`echo $current_release_version | rev | cut -d'.' -f2- | rev`" == "`echo $Branch_Name | rev | cut -d'.' -f2- | rev`" ]]; then
    echo "Branch Name version given is matching current Major/Minor release version. Validating on HF version..."
    major_minor_version=`echo $current_release_version | rev | cut -d'.' -f2- | rev`
    for branch in `git branch -r | grep -w "^${major_minor_version}.[0-99]$"`
    do
      branch=`echo $branch | cut -d"/" -f2`
      if [[ ${branch} =~ ^Release-v[0-9][0-9]?\.[0-9][0-9]?\.[0-9][0-9]?$ ]]; then
        hf_version_old=`echo ${branch} | cut -d"-" -f2 | cut -d"." -f3`
        #HF check
        if [ "$hf_version_new" -gt "$hf_version_old" ]; then
            echo "*****HF check PASS wrt ${branch}*****"
        else
            echo "*****Release version ${Branch_Name} is INVALID wrt ${version_history}*****"
            exit 1
        fi
      fi
    done
    echo "*****Release version syntax ${Branch_Name} is VALID*****"
    exit 0
  fi

  #Checking for Major/Minor release version
  for version_history in `git branch -r`
  do
    version_history=`echo $version_history | cut -d"/" -f2`
    if [[ ${version_history} =~ ^Release-v[0-9][0-9]?\.[0-9][0-9]?\.[0-9][0-9]?$ ]]; then
      major_version_old=`echo ${version_history} | cut -d"-" -f2 | cut -d"." -f1 | cut -c2`
      minor_version_old=`echo ${version_history} | cut -d"-" -f2 | cut -d"." -f2`
      #major check
      if [ "$major_version_new" -gt "$major_version_old" ]; then
        echo "*****Major check PASS wrt ${version_history}*****"
      elif [ "$major_version_new" -eq "$major_version_old" ]; then
      #minor check
        if [ "$minor_version_new" -gt "$minor_version_old" ]; then
          echo "*****Minor check PASS wrt ${version_history}*****"
        elif [ "$minor_version_new" -lt "$minor_version_old" ]; then
          echo "*****Release version ${Branch_Name} is INVALID wrt ${version_history}*****"
          exit 1
        fi
      else
        echo "*****Release version ${Branch_Name} is INVALID wrt ${version_history}*****"
        exit 1
      fi
    fi
  done
  echo "*****Release version syntax ${Branch_Name} is VALID*****"
else
  echo "*****Release version syntax invalid or branch already present*****"
  exit 1
fi