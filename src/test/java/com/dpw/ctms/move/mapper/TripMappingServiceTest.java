package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.entity.ResourceAssignmentDetails;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.enums.AssignmentStatus;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.response.TripListingResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

public class TripMappingServiceTest {

    private TripMappingService tripMappingService;

    private Trip trip;

    @BeforeEach
    void setUp() {
        tripMappingService = Mappers.getMapper(TripMappingService.class);
        trip = createCompleteTrip();
    }

    @Test
    void mapToResponse_WithCompleteTrip_ShouldMapAllFields() {
        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);
        assertEquals("TRIP001", response.getCode());
        assertEquals(TripStatus.ACTIVE.name(), response.getStatus().getValue());
        assertEquals("TO001", response.getTransportOrderCode());
        
        // Verify customer orders
        assertNotNull(response.getCustomerOrders());
        assertEquals(2, response.getCustomerOrders().size());
        assertEquals("CO001", response.getCustomerOrders().get(0).getId());
        assertEquals("CO002", response.getCustomerOrders().get(1).getId());

        // Verify customer order timestamps
        assertEquals(5100L, response.getCustomerOrders().get(0).getUpdatedAt());
        assertEquals(5300L, response.getCustomerOrders().get(1).getUpdatedAt());
        
        // Verify assignment details
        assertNotNull(response.getAssignmentDetails());
        assertEquals(AssignmentType.EXTERNAL.name(), response.getAssignmentDetails().getType().getValue());
        assertEquals("VENDOR001", response.getAssignmentDetails().getVendor().getCode());

        // Verify assignment details timestamps
        assertEquals(1100L, response.getAssignmentDetails().getUpdatedAt());
        
        // Verify resources
        assertNotNull(response.getResources());
        assertEquals(1, response.getResources().getVehicles().size());
        assertEquals("VEHICLE001", response.getResources().getVehicles().get(0).getId());
        assertEquals("TRUCK", response.getResources().getVehicles().get(0).getTypeCode());
        assertEquals("REG001", response.getResources().getVehicles().get(0).getRegistrationNumber());

        // Verify vehicle timestamps
        assertEquals(2100L, response.getResources().getVehicles().get(0).getUpdatedAt());

        assertEquals(2, response.getResources().getTrailers().size());
        assertEquals("TRAILER001", response.getResources().getTrailers().get(0).getId());

        // Verify trailer timestamps
        assertEquals(3100L, response.getResources().getTrailers().get(0).getUpdatedAt());

        assertEquals(1, response.getResources().getVehicleOperators().size());
        assertEquals("DRIVER001", response.getResources().getVehicleOperators().get(0).getId());
        assertEquals(AssignmentStatus.ASSIGNED.name(), response.getResources().getVehicleOperators().get(0).getStatus().getValue());

        // Verify vehicle operator timestamps
        assertEquals(4100L, response.getResources().getVehicleOperators().get(0).getUpdatedAt());

        // Verify locations
        assertNotNull(response.getLocations());
        assertEquals("ORIGIN001", response.getLocations().getOrigin().getCode());
        assertEquals("DEST001", response.getLocations().getDestination().getCode());

        // Verify trip-level timestamps
        assertEquals(6100L, response.getUpdatedAt());
    }

    @Test
    void mapToResponse_WithNullTrip_ShouldReturnNull() {
        // Act
        TripListingResponse response = tripMappingService.mapToResponse(null);

        // Assert
        assertNull(response);
    }

    @Test
    void mapToResponse_WithNullTransportOrder_ShouldHandleGracefully() {
        // Arrange
        trip.setTransportOrder(null);

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);
        assertEquals("TRIP001", response.getCode());
        assertEquals(TripStatus.ACTIVE.name(), response.getStatus().getValue());
        assertNull(response.getTransportOrderCode());
        
        // Verify assignment details still present but with null type and vendor since transport order is null
        assertNotNull(response.getAssignmentDetails());
        assertNull(response.getAssignmentDetails().getType());
        assertNull(response.getAssignmentDetails().getVendor());
        assertNull(response.getAssignmentDetails().getUpdatedAt());
    }

    @Test
    void mapToResponse_WithEmptyShipments_ShouldHandleGracefully() {
        // Arrange
        trip.setShipments(Collections.emptySet());

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);
        assertEquals("TRIP001", response.getCode());
        assertEquals(TripStatus.ACTIVE.name(), response.getStatus().getValue());
        
        // Verify empty collections
        assertTrue(response.getCustomerOrders().isEmpty());
        
        // Verify time ranges are empty
        assertNotNull(response.getExpectedTimes());
        assertNull(response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());
        
        assertNotNull(response.getActualTimes());
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
    }

    @Test
    void mapToResponse_WithEmptyStops_ShouldHandleGracefully() {
        // Arrange
        trip.setStops(Collections.emptySet());

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);
        assertEquals("TRIP001", response.getCode());
        assertEquals(TripStatus.ACTIVE.name(), response.getStatus().getValue());

    }

    @Test
    void mapToResponse_WithNullVehicleResource_ShouldHandleGracefully() {
        // Arrange
        trip.setVehicleResource(null);

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);
        assertEquals("TRIP001", response.getCode());
        assertEquals(TripStatus.ACTIVE.name(), response.getStatus().getValue());
        
        // Verify empty vehicles collection
        assertTrue(response.getResources().getVehicles().isEmpty());
    }

    @Test
    void mapToResponse_WithNullStatus_ShouldHandleGracefully() {
        // Arrange
        trip.setStatus(null);

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);
        assertEquals("TRIP001", response.getCode());
        assertNull(response.getStatus());
    }

    @Test
    void mapToResponse_WithTripTimestamps_ShouldUseDirectTripTimestamps() {
        // Arrange - Set Trip entity timestamps directly
        trip.setExpectedPickupAt(1000L);
        trip.setExpectedDeliveryAt(2500L);
        trip.setActualPickupAt(1100L);
        trip.setActualDeliveryAt(2600L);

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);

        // Verify expected time ranges use Trip entity timestamps
        assertEquals(1000L, response.getExpectedTimes().getStartAt()); // Trip expected pickup
        assertEquals(2500L, response.getExpectedTimes().getEndAt()); // Trip expected delivery

        // Verify actual time ranges use Trip entity timestamps
        assertEquals(1100L, response.getActualTimes().getStartAt()); // Trip actual pickup
        assertEquals(2600L, response.getActualTimes().getEndAt()); // Trip actual delivery
    }

    @Test
    void mapToResponse_WithPartialTripTimestamps_ShouldHandleNullValues() {
        // Arrange - Set only some Trip entity timestamps
        trip.setExpectedPickupAt(1000L);
        trip.setExpectedDeliveryAt(null); // Null expected delivery
        trip.setActualPickupAt(null); // Null actual pickup
        trip.setActualDeliveryAt(2100L);

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);

        // Verify expected time ranges handle null values correctly
        assertEquals(1000L, response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());

        // Verify actual time ranges handle null values correctly
        assertNull(response.getActualTimes().getStartAt());
        assertEquals(2100L, response.getActualTimes().getEndAt());
    }

    @Test
    void mapToResponse_WithAllNullTripTimestamps_ShouldReturnNullValues() {
        // Arrange - All Trip entity timestamps are null
        trip.setExpectedPickupAt(null);
        trip.setExpectedDeliveryAt(null);
        trip.setActualPickupAt(null);
        trip.setActualDeliveryAt(null);

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);

        // Verify all timestamp fields are null
        assertNull(response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
    }



    @Test
    void mapToResponse_WithNullTimestamps_ShouldHandleGracefully() {
        // Arrange - Create entities with null timestamps
        trip.setUpdatedAt(null);

        trip.getTransportOrder().setUpdatedAt(null);

        trip.getVehicleResource().setUpdatedAt(null);

        trip.getShipments().forEach(shipment -> {
            shipment.setUpdatedAt(null);
        });

        // Act
        TripListingResponse response = tripMappingService.mapToResponse(trip);

        // Assert
        assertNotNull(response);

        // Verify null timestamps are handled gracefully at trip level
        assertNull(response.getUpdatedAt());

        // Verify null timestamps are handled gracefully for nested objects
        assertNull(response.getAssignmentDetails().getUpdatedAt());

        assertNull(response.getResources().getVehicles().get(0).getUpdatedAt());

        response.getCustomerOrders().forEach(customerOrder -> {
            assertNull(customerOrder.getUpdatedAt());
        });
    }



    private Trip createCompleteTrip() {
        Trip trip = new Trip();
        trip.setCode("TRIP001");
        trip.setStatus(TripStatus.ACTIVE);
        trip.setExternalOriginLocationCode("ORIGIN001");
        trip.setExternalDestinationLocationCode("DEST001");
        trip.setUpdatedAt(6100L);

        // Transport Order
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO001");
        transportOrder.setAssigneeIdentifier("VENDOR001");
        transportOrder.setAssignmentType(AssignmentType.EXTERNAL);
        transportOrder.setUpdatedAt(1100L);
        trip.setTransportOrder(transportOrder);

        // Vehicle Resource
        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId("VEHICLE001");
        vehicleResource.setExternalVehicleTypeId("TRUCK");
        vehicleResource.setRegistrationNumber("REG001");
        vehicleResource.setUpdatedAt(2100L);
        vehicleResource.setTrip(trip);
        trip.setVehicleResource(vehicleResource);

        // Trailer Resources
        Set<TrailerResource> trailerResources = new LinkedHashSet<>() ;
        TrailerResource trailer1 = new TrailerResource();
        trailer1.setExternalResourceId("TRAILER001");
        trailer1.setUpdatedAt(3100L);
        trailer1.setTrip(trip);

        TrailerResource trailer2 = new TrailerResource();
        trailer2.setExternalResourceId("TRAILER002");
        trailer2.setUpdatedAt(3300L);
        trailer2.setTrip(trip);

        trailerResources.add(trailer1);
        trailerResources.add(trailer2);
        trip.setTrailerResources(trailerResources);
        
        // Vehicle Operator Resources
        Set<VehicleOperatorResource> operatorResources = new LinkedHashSet<>() ;
        VehicleOperatorResource operator = new VehicleOperatorResource();
        operator.setExternalResourceId("DRIVER001");
        operator.setUpdatedAt(4100L);
        operator.setTrip(trip);

        // Set up resource assignment details
        ResourceAssignmentDetails operatorAssignmentDetails = new ResourceAssignmentDetails();
        operatorAssignmentDetails.setAssignmentStatus(AssignmentStatus.ASSIGNED);
        operator.setResourceAssignmentDetails(operatorAssignmentDetails);

        operatorResources.add(operator);
        trip.setVehicleOperatorResources(operatorResources);
        
        // Shipments
        Set<Shipment> shipments = new LinkedHashSet<>() ;
        Shipment shipment1 = new Shipment();
        shipment1.setCode("SHIPMENT001");
        shipment1.setExternalCustomerOrderId("CO001");
        shipment1.setStatus(ShipmentStatus.ALLOCATED);
        shipment1.setUpdatedAt(5100L);
        shipment1.setTrip(trip);

        Shipment shipment2 = new Shipment();
        shipment2.setCode("SHIPMENT002");
        shipment2.setExternalCustomerOrderId("CO002");
        shipment2.setStatus(ShipmentStatus.ALLOCATED);
        shipment2.setUpdatedAt(5300L);
        shipment2.setTrip(trip);

        shipments.add(shipment1);
        shipments.add(shipment2);
        trip.setShipments(shipments);
        
        // Stops
        Set<Stop> stops = new LinkedHashSet<>();
        Stop stop1 = new Stop();
        stop1.setCode("STOP001");
        stop1.setExternalLocationCode("LOC001");
        stop1.setSequence(1);
        stop1.setUpdatedAt(8100L);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP002");
        stop2.setExternalLocationCode("LOC002");
        stop2.setSequence(2);
        stop2.setUpdatedAt(8300L);
        stop2.setTrip(trip);

        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);
        
        return trip;
    }
}