package com.dpw.ctms.move.kafka.consumer;

import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.PickupTaskMessageDTO;
import org.springframework.stereotype.Service;

@Service
public class PickupTaskHandler implements TaskActionHandler<PickupTaskMessageDTO> {
    @Override
    public void handle(IntegratorMessageRequestDTO<PickupTaskMessageDTO> message) {
    }
}
