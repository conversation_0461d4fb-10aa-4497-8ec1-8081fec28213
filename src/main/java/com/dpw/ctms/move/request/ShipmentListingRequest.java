package com.dpw.ctms.move.request;

import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.Valid;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentListingRequest {
    private Pagination pagination;

    private Sort sort;

    private Filter filter;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Filter {
        private List<String> shipmentIds;
        private List<String> tripCodes;
        private List<String> consignmentIds;
        private List<String> shipmentStatuses;
        private List<String> transportOrderCodes;
        private List<String> customerOrderIds;
        private List<String> originStops;
        private List<String> destinationStops;

        @Valid
        private DateRange expectedPickupDateRange;

        @Valid
        private DateRange expectedDeliveryDateRange;
    }
}
