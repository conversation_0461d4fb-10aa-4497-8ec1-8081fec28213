package com.dpw.ctms.move.kafka.consumer;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;

@EnableKafka
@Configuration
public class KafkaPickupTaskConsumerConfig {
    @Value("${kafka.consumer.shipment-task-acknowledgement.bootstrap-servers:eh-cargoestms-dev-03.servicebus.windows.net:9093}")
    private String bootstrapAddress;

    @Value("${kafka.consumer.shipment-task-acknowledgement.consumer-group-id:dpw-task-execution-results-dev}")
    private String consumerGroupId;

    @Value("${kafka.consumer.shipment-task-acknowledgement.username:username}")
    private String userName;

    @Value("${kafka.consumer.shipment-task-acknowledgement.password:password}")
    private String password;

    @Value("${kafka.security.protocol:SASL_SSL}")
    private String securityProtocol;

    @Value("${kafka.sasl.mechanism:PLAIN}")
    private String saslMechanism;

    @Value("${kafka.consumer.shipment-task-acknowledgement.concurrent-consumers:1}")
    private int concurrentConsumers;

    public Map<String, Object> consumerConfigs(String groupId, String bootStrapServers) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put("security.protocol", securityProtocol);
        props.put("sasl.mechanism", saslMechanism);
        props.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"" +
                userName + "\" password=\"" + password + "\";");
        props.put("spring.json.trusted.packages", "*");
        return props;
    }

    public ConsumerFactory<String, String> consumerFactory(String bootStrapServers) {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs(consumerGroupId, bootStrapServers));
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> shipmentTaskContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory(bootstrapAddress));
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.RECORD);
        factory.setConcurrency(concurrentConsumers);
        return factory;
    }
}
