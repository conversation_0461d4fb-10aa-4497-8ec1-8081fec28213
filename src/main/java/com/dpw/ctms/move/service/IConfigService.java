package com.dpw.ctms.move.service;

import com.dpw.ctms.move.enums.ConfigType;

public interface IConfigService {
    /** If a new config needs to added, this interface method has to be implemented in the respective config service
     * class. ConfigServiceIntegrator class will be responsible for automatically registering that newly implemented
     * config class. **/
    ConfigType getConfigType();
}
