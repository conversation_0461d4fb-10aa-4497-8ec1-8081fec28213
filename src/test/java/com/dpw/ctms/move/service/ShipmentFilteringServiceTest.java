package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.ShipmentFieldConstants;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.mapper.ShipmentMapper;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ShipmentFilteringServiceTest {

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private ShipmentMapper shipmentMapper;

    @InjectMocks
    private ShipmentFilteringService shipmentFilteringService;

    @Captor
    private ArgumentCaptor<Pageable> pageableCaptor;

    private ShipmentListingRequest shipmentListingRequest;
    private List<Shipment> shipmentEntities;
    private List<ShipmentListingResponse> shipmentResponses;

    @BeforeEach
    void setUp() {
        // Setup request
        ShipmentListingRequest.Filter filter = createShipmentIdsFilter("SHIP001", "SHIP002");
        filter.setShipmentStatuses(Arrays.asList(ShipmentStatus.ASSIGNED.name(), "ALLOCATED"));

        shipmentListingRequest = createCustomShipmentListingRequest(
                createDefaultPagination(),
                createCreatedAtDescSort(),
                filter
        );

        // Setup shipment entities
        shipmentEntities = Arrays.asList(
                createShipmentEntity("SHIP001", ShipmentStatus.ASSIGNED),
                createShipmentEntity("SHIP002", ShipmentStatus.ALLOCATED)
        );

        // Setup shipment responses
        shipmentResponses = Arrays.asList(
                createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED),
                createShipmentResponse("SHIP002", ShipmentStatus.ALLOCATED)
        );
    }

    @Test
    void filterShipments_WithValidRequest_ShouldReturnFilteredShipments() {
        // Arrange
        Page<Shipment> shipmentPage = new PageImpl<>(shipmentEntities, Pageable.ofSize(10), 2);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipmentEntities.get(0))).thenReturn(shipmentResponses.get(0));
        when(shipmentMapper.toResponse(shipmentEntities.get(1))).thenReturn(shipmentResponses.get(1));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(shipmentListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        assertEquals("SHIP001", response.getData().get(0).getCode());
        assertEquals("SHIP002", response.getData().get(1).getCode());

        verify(shipmentRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(shipmentMapper, times(2)).toResponse(any(Shipment.class));
    }

    @Test
    void filterShipments_WithNullFilter_ShouldReturnAllShipments() {
        // Arrange
        ShipmentListingRequest requestWithNullFilter = createCustomShipmentListingRequest(
                createDefaultPagination(),
                createCreatedAtDescSort(),
                null
        );

        Page<Shipment> shipmentPage = new PageImpl<>(shipmentEntities, Pageable.ofSize(10), 2);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(any(Shipment.class))).thenReturn(shipmentResponses.get(0));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithNullFilter);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        verify(shipmentRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void filterShipments_WithEmptyResult_ShouldReturnEmptyList() {
        // Arrange
        Page<Shipment> emptyPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(emptyPage);

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(shipmentListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
        assertEquals(0, response.getData().size());
    }

    @Test
    void filterShipments_WithException_ShouldThrowRuntimeException() {
        // Arrange
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                shipmentFilteringService.filterShipments(shipmentListingRequest));

        assertEquals("Failed to filter shipments", exception.getMessage());
        assertNotNull(exception.getCause());
        assertEquals("Database error", exception.getCause().getMessage());
    }

    @Test
    void filterShipments_WithDefaultPagination_ShouldUseDefaults() {
        // Arrange
        ShipmentListingRequest requestWithNullPagination = createCustomShipmentListingRequest(
                null, null, new ShipmentListingRequest.Filter()
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(20), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(shipmentPage);

        // Act
        shipmentFilteringService.filterShipments(requestWithNullPagination);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber()); // Default page number
        assertEquals(20, pageable.getPageSize()); // Default page size
        assertEquals("createdAt: DESC", pageable.getSort().toString()); // Default sort
    }

    @Test
    void filterShipments_WithLargePageSize_ShouldLimitToMaxPageSize() {
        // Arrange
        Pagination largePagination = new Pagination();
        largePagination.setPageNo(0);
        largePagination.setPageSize(1000); // Exceeds max of 500

        ShipmentListingRequest requestWithLargePage = createCustomShipmentListingRequest(
                largePagination, null, new ShipmentListingRequest.Filter()
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(500), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(shipmentPage);

        // Act
        shipmentFilteringService.filterShipments(requestWithLargePage);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(500, pageable.getPageSize()); // Max page size
    }

    @Test
    void filterShipments_WithNegativePageNumber_ShouldUseZero() {
        // Arrange
        Pagination negativePagination = new Pagination();
        negativePagination.setPageNo(-1);
        negativePagination.setPageSize(10);

        ShipmentListingRequest requestWithNegativePage = createCustomShipmentListingRequest(
                negativePagination, null, new ShipmentListingRequest.Filter()
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(shipmentPage);

        // Act
        shipmentFilteringService.filterShipments(requestWithNegativePage);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber()); // Should be corrected to 0
    }

    // Helper methods
    private ShipmentListingRequest.Filter createShipmentIdsFilter(String... shipmentIds) {
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Arrays.asList(shipmentIds));
        return filter;
    }

    private ShipmentListingRequest createCustomShipmentListingRequest(Pagination pagination, Sort sort, ShipmentListingRequest.Filter filter) {
        ShipmentListingRequest request = new ShipmentListingRequest();
        request.setPagination(pagination);
        request.setSort(sort);
        request.setFilter(filter);
        return request;
    }

    private Pagination createDefaultPagination() {
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);
        return pagination;
    }

    private Sort createCreatedAtDescSort() {
        Sort sort = new Sort();
        sort.setSortBy(ShipmentFieldConstants.SortFields.API_CREATED_AT);
        sort.setSortOrder("DESC");
        return sort;
    }

    private Shipment createShipmentEntity(String code, ShipmentStatus status) {
        Shipment shipment = new Shipment();
        shipment.setCode(code);
        shipment.setStatus(status);
        shipment.setCreatedAt(System.currentTimeMillis());
        shipment.setUpdatedAt(System.currentTimeMillis());
        return shipment;
    }

    private ShipmentListingResponse createShipmentResponse(String code, ShipmentStatus status) {
        return ShipmentListingResponse.builder()
                .code(code)
                .status(new EnumLabelValueResponse(status.getDisplayName(), status.name()))
                .updatedAt(System.currentTimeMillis())
                .build();
    }

    @Test
    void filterShipments_WithShipmentIds_ShouldFilterByShipmentIds() {
        // Arrange
        ShipmentListingRequest.Filter filterWithShipmentIds = createShipmentIdsFilter("SHIP001");
        ShipmentListingRequest requestWithShipmentIds = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithShipmentIds
        );

        Shipment shipment = createShipmentEntity("SHIP001", ShipmentStatus.ASSIGNED);
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithShipmentIds);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
        assertEquals("SHIP001", response.getData().get(0).getCode());
    }

    @Test
    void filterShipments_WithInvalidShipmentIds_ShouldHandleGracefully() {
        // Arrange
        ShipmentListingRequest.Filter filterWithInvalidIds = new ShipmentListingRequest.Filter();
        filterWithInvalidIds.setShipmentIds(Arrays.asList("", null, "   ", "VALID_ID"));

        ShipmentListingRequest requestWithInvalidIds = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithInvalidIds
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithInvalidIds);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
    }

    @Test
    void filterShipments_WithShipmentStatuses_ShouldFilterByShipmentStatuses() {
        // Arrange
        ShipmentListingRequest.Filter filterWithStatuses = new ShipmentListingRequest.Filter();
        filterWithStatuses.setShipmentStatuses(Arrays.asList("ASSIGNED"));

        ShipmentListingRequest requestWithStatuses = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithStatuses
        );

        Shipment shipment = createShipmentEntity("SHIP001", ShipmentStatus.ASSIGNED);
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithStatuses);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithInvalidShipmentStatus_ShouldIgnoreInvalidStatus() {
        // Arrange
        ShipmentListingRequest.Filter filterWithInvalidStatus = new ShipmentListingRequest.Filter();
        filterWithInvalidStatus.setShipmentStatuses(Arrays.asList("ASSIGNED", "INVALID_STATUS"));

        ShipmentListingRequest requestWithInvalidStatus = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithInvalidStatus
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithInvalidStatus);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
    }

    @Test
    void filterShipments_WithTripCodes_ShouldFilterByTripCodes() {
        // Arrange
        ShipmentListingRequest.Filter filterWithTripCodes = new ShipmentListingRequest.Filter();
        filterWithTripCodes.setTripCodes(Arrays.asList("TRIP001"));

        ShipmentListingRequest requestWithTripCodes = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithTripCodes
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithTripCodes);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    private Shipment createComplexShipmentEntity() {
        Shipment shipment = createShipmentEntity("SHIP001", ShipmentStatus.ASSIGNED);

        // Add trip
        Trip trip = new Trip();
        trip.setCode("TRIP001");
        shipment.setTrip(trip);

        // Add transport order
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO001");
        shipment.setTransportOrder(transportOrder);

        // Add stops
        Stop originStop = new Stop();
        originStop.setExternalLocationCode("ORIGIN001");
        shipment.setOriginStop(originStop);

        Stop destinationStop = new Stop();
        destinationStop.setExternalLocationCode("DEST001");
        shipment.setDestinationStop(destinationStop);

        // Add other fields
        shipment.setExternalConsignmentId("CONSIGNMENT001");
        shipment.setExternalCustomerOrderId("CO001");
        shipment.setExpectedPickupAt(1500L);
        shipment.setExpectedDeliveryAt(1800L);

        return shipment;
    }

    @Test
    void filterShipments_WithConsignmentIds_ShouldFilterByConsignmentIds() {
        // Arrange
        ShipmentListingRequest.Filter filterWithConsignmentIds = new ShipmentListingRequest.Filter();
        filterWithConsignmentIds.setConsignmentIds(Arrays.asList("CONSIGNMENT001"));

        ShipmentListingRequest requestWithConsignmentIds = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithConsignmentIds
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithConsignmentIds);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithTransportOrderCodes_ShouldFilterByTransportOrderCodes() {
        // Arrange
        ShipmentListingRequest.Filter filterWithTransportOrderCodes = new ShipmentListingRequest.Filter();
        filterWithTransportOrderCodes.setTransportOrderCodes(Arrays.asList("TO001"));

        ShipmentListingRequest requestWithTransportOrderCodes = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithTransportOrderCodes
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithTransportOrderCodes);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithCustomerOrderIds_ShouldFilterByCustomerOrderIds() {
        // Arrange
        ShipmentListingRequest.Filter filterWithCustomerOrderIds = new ShipmentListingRequest.Filter();
        filterWithCustomerOrderIds.setCustomerOrderIds(Arrays.asList("CO001"));

        ShipmentListingRequest requestWithCustomerOrderIds = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithCustomerOrderIds
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithCustomerOrderIds);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithOriginStops_ShouldFilterByOriginStops() {
        // Arrange
        ShipmentListingRequest.Filter filterWithOriginStops = new ShipmentListingRequest.Filter();
        filterWithOriginStops.setOriginStops(Arrays.asList("ORIGIN001"));

        ShipmentListingRequest requestWithOriginStops = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithOriginStops
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithOriginStops);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithDestinationStops_ShouldFilterByDestinationStops() {
        // Arrange
        ShipmentListingRequest.Filter filterWithDestinationStops = new ShipmentListingRequest.Filter();
        filterWithDestinationStops.setDestinationStops(Arrays.asList("DEST001"));

        ShipmentListingRequest requestWithDestinationStops = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithDestinationStops
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithDestinationStops);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithExpectedPickupDateRange_ShouldFilterByExpectedPickupDateRange() {
        // Arrange
        DateRange dateRange = new DateRange();
        dateRange.setFrom(1000L);
        dateRange.setTo(2000L);

        ShipmentListingRequest.Filter filterWithExpectedDates = new ShipmentListingRequest.Filter();
        filterWithExpectedDates.setExpectedPickupDateRange(dateRange);

        ShipmentListingRequest requestWithExpectedDates = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithExpectedDates
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithExpectedDates);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithExpectedDeliveryDateRange_ShouldFilterByExpectedDeliveryDateRange() {
        // Arrange
        DateRange dateRange = new DateRange();
        dateRange.setFrom(1700L);
        dateRange.setTo(2000L);

        ShipmentListingRequest.Filter filterWithExpectedDeliveryDates = new ShipmentListingRequest.Filter();
        filterWithExpectedDeliveryDates.setExpectedDeliveryDateRange(dateRange);

        ShipmentListingRequest requestWithExpectedDeliveryDates = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithExpectedDeliveryDates
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithExpectedDeliveryDates);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithPartialDateRange_ShouldHandleFromDateOnly() {
        // Arrange
        DateRange dateRange = new DateRange();
        dateRange.setFrom(1000L);
        // No 'to' date set

        ShipmentListingRequest.Filter filterWithFromDate = new ShipmentListingRequest.Filter();
        filterWithFromDate.setExpectedPickupDateRange(dateRange);

        ShipmentListingRequest requestWithFromDate = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithFromDate
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithFromDate);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithPartialDateRange_ShouldHandleToDateOnly() {
        // Arrange
        DateRange dateRange = new DateRange();
        dateRange.setTo(2000L);
        // No 'from' date set

        ShipmentListingRequest.Filter filterWithToDate = new ShipmentListingRequest.Filter();
        filterWithToDate.setExpectedPickupDateRange(dateRange);

        ShipmentListingRequest requestWithToDate = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithToDate
        );

        Shipment shipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithToDate);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithInvalidSortField_ShouldUseDefaultSort() {
        // Arrange
        Sort invalidSort = new Sort();
        invalidSort.setSortBy("invalidField");
        invalidSort.setSortOrder("ASC");

        ShipmentListingRequest requestWithInvalidSort = createCustomShipmentListingRequest(
                createDefaultPagination(), invalidSort, new ShipmentListingRequest.Filter()
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(20), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(shipmentPage);

        // Act
        shipmentFilteringService.filterShipments(requestWithInvalidSort);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("createdAt: ASC", pageable.getSort().toString()); // Should use default field with provided order
    }

    @Test
    void filterShipments_WithInvalidSortOrder_ShouldUseDefaultOrder() {
        // Arrange
        Sort invalidSort = new Sort();
        invalidSort.setSortBy("code");
        invalidSort.setSortOrder("INVALID_ORDER");

        ShipmentListingRequest requestWithInvalidSort = createCustomShipmentListingRequest(
                createDefaultPagination(), invalidSort, new ShipmentListingRequest.Filter()
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(20), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(shipmentPage);

        // Act
        shipmentFilteringService.filterShipments(requestWithInvalidSort);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("code: DESC", pageable.getSort().toString()); // Should use provided field with default order
    }

    @Test
    void filterShipments_WithEmptyCollections_ShouldHandleGracefully() {
        // Arrange
        ShipmentListingRequest.Filter filterWithEmptyCollections = new ShipmentListingRequest.Filter();
        filterWithEmptyCollections.setShipmentIds(Collections.emptyList());
        filterWithEmptyCollections.setShipmentStatuses(Collections.emptyList());
        filterWithEmptyCollections.setTripCodes(Collections.emptyList());
        filterWithEmptyCollections.setConsignmentIds(Collections.emptyList());
        filterWithEmptyCollections.setTransportOrderCodes(Collections.emptyList());
        filterWithEmptyCollections.setCustomerOrderIds(Collections.emptyList());
        filterWithEmptyCollections.setOriginStops(Collections.emptyList());
        filterWithEmptyCollections.setDestinationStops(Collections.emptyList());

        ShipmentListingRequest requestWithEmptyCollections = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), filterWithEmptyCollections
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithEmptyCollections);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
        assertTrue(response.getData().isEmpty());
    }

    @Test
    void filterShipments_WithAllFilterCriteria_ShouldApplyAllFilters() {
        // Arrange
        ShipmentListingRequest.Filter complexFilter = createComplexShipmentFilter();
        ShipmentListingRequest complexRequest = createCustomShipmentListingRequest(
                createDefaultPagination(), createCreatedAtDescSort(), complexFilter
        );

        Shipment complexShipment = createComplexShipmentEntity();
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(complexShipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(complexShipment)).thenReturn(createShipmentResponse("SHIP001", ShipmentStatus.ASSIGNED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(complexRequest);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
        assertEquals("SHIP001", response.getData().get(0).getCode());
    }

    @Test
    void filterShipments_WithZeroPageSize_ShouldUseDefaultPageSize() {
        // Arrange
        Pagination zeroPageSizePagination = new Pagination();
        zeroPageSizePagination.setPageNo(0);
        zeroPageSizePagination.setPageSize(0);

        ShipmentListingRequest requestWithZeroPageSize = createCustomShipmentListingRequest(
                zeroPageSizePagination, null, new ShipmentListingRequest.Filter()
        );

        Page<Shipment> shipmentPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(20), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(shipmentPage);

        // Act
        shipmentFilteringService.filterShipments(requestWithZeroPageSize);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(20, pageable.getPageSize()); // Should use default page size
    }

    private ShipmentListingRequest.Filter createComplexShipmentFilter() {
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Arrays.asList("SHIP001", "SHIP002"));
        filter.setShipmentStatuses(Arrays.asList("ASSIGNED", "ALLOCATED"));
        filter.setTripCodes(Arrays.asList("TRIP001"));
        filter.setConsignmentIds(Arrays.asList("CONSIGNMENT001"));
        filter.setTransportOrderCodes(Arrays.asList("TO001"));
        filter.setCustomerOrderIds(Arrays.asList("CO001"));
        filter.setOriginStops(Arrays.asList("ORIGIN001"));
        filter.setDestinationStops(Arrays.asList("DEST001"));

        DateRange expectedPickupRange = new DateRange();
        expectedPickupRange.setFrom(1000L);
        expectedPickupRange.setTo(1200L);
        filter.setExpectedPickupDateRange(expectedPickupRange);

        DateRange expectedDeliveryRange = new DateRange();
        expectedDeliveryRange.setFrom(2000L);
        expectedDeliveryRange.setTo(2200L);
        filter.setExpectedDeliveryDateRange(expectedDeliveryRange);

        return filter;
    }
}
