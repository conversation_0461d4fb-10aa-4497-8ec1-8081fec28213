trigger:
  branches:
    include:
    - development

variables:
  - group: TMS
  - name: GRADLE_USER_HOME
    value: $(Pipeline.Workspace)/.gradle

pool:
  vmImage: 'ubuntu-latest'

name: $(Build.SourceBranchName)_$(Rev:r)

stages:
  - stage: Build
    jobs:
      - job: BuildDev
        displayName: 'Build on dev branch'
        steps:
          - script: mkdir -p $(GRADLE_USER_HOME)
            displayName: 'Ensure Gradle Directory Exists'

          - task: Cache@2
            inputs:
              key: 'gradle | "$(Agent.OS)" | **/build.gradle'
              restoreKeys: |
                gradle | "$(Agent.OS)"
                gradle
              path:  $(GRADLE_USER_HOME)
            displayName: 'Configure Gradle Caching'

          - task: Gradle@2
            inputs:
              workingDirectory: '$(System.DefaultWorkingDirectory)'
              gradleWrapperFile: 'gradlew'
              gradleOptions: '-Xmx3072m'
              publishJUnitResults: false
              tasks: 'clean build -x test'
              options: '--build-cache'
              javaHomeOption: 'JDKVersion'
              jdkVersionOption: '1.21'
              wrapperScript: 'gradlew'
            displayName: 'Run Gradle Build'
            env:
              SYSTEM_ACCESSTOKEN: $(System.AccessToken)            

          - task: Veracode@3
            inputs:
              ConnectionDetailsSelection: 'Endpoint'
              AnalysisService: 'VeracodeConnection'
              veracodeAppProfile: 'ctms-move'
              version: '$(Build.BuildId)'
              filepath: '$(System.DefaultWorkingDirectory)/build/libs/ctsm-move-0.0.1-SNAPSHOT.jar'
              failBuildOnPolicyFail: false
              maximumWaitTime: '360'
            displayName: 'Run Veracode Analysis'
     
          - task: Docker@2
            displayName: 'Build & Push Docker Image'
            inputs:
              containerRegistry: 'acrcargoestmsnonprod'
              repository: 'ctms-move'
              arguments: '--build-arg SYSTEM_ACCESSTOKEN=$(artifactory-token)'
              command: 'buildAndPush'
              Dockerfile: 'Dockerfile'
              tags: |
                $(Build.SourceVersion)
                latest
       
          - script: |
              python -m pip install --upgrade pip
              pip install -r DevOps_Scripts/requirements.txt
            displayName: 'Install Python Packages from requirements.txt'

          - task: PythonScript@0
            displayName: 'Create PR to Manifest Repo for Dev'
            inputs:
              scriptSource: 'filePath'
              scriptPath: DevOps_Scripts/create_pr.py
              arguments: $(System.AccessToken) "tms-stack" $(Build.SourceBranchName) $(Build.SourceVersion) "ctms-move"
          
          - script: |   
              # Stop the Gradle daemon to ensure no files are left open (impacting the save cache operation later)
              ./gradlew --stop    
            displayName: 'Stop Gradle Daemon'
