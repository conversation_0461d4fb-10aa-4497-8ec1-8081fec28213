package com.dpw.ctms.move.testcontainers;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;


/**
 * Manages test database operations including schema isolation and cleanup.
 * Ensures each test class gets a clean database state without container conflicts.
 */
@Slf4j
public class TestDatabaseManager {
    /**
     * Cleans up all test data from the CFR schema (used by most tests).
     * This is safer than dropping the schema as it preserves the structure.
     */
    public static void cleanupCfrSchema() {
        try {
            JdbcTemplate jdbcTemplate = createJdbcTemplate();
            
            String[] tablesToClean = {
                "CFR.trip",
                "CFR.stop",
                "CFR.transport_order",
                "CFR.shipment"
            };
            
            for (String table : tablesToClean) {
                try {
                    jdbcTemplate.execute("TRUNCATE TABLE " + table + " CASCADE");
                    log.info("Cleaned table: {}", table);
                } catch (Exception e) {
                    // Table might not exist, which is fine
                    log.info("Could not clean table {} (might not exist): {}", table, e.getMessage());
                }
            }
            
            // Reset sequences if they exist
            String[] sequencesToReset = {
                "CFR.trip_id_seq",
                "CFR.transport_order_id_seq",
                "CFR.stop_id_seq",
                "CFR.shipment_id_seq"
            };
            
            for (String sequence : sequencesToReset) {
                try {
                    jdbcTemplate.execute("ALTER SEQUENCE " + sequence + " RESTART WITH 1");
                    log.info("Reset sequence: {}", sequence);
                } catch (Exception e) {
                    // Sequence might not exist, which is fine
                    log.info("Could not reset sequence {} (might not exist): {}", sequence, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.warn("Failed to cleanup CFR schema: {}", e.getMessage());
        }
    }
    
    /**
     * Ensures the cfr schema exists (required for multi-tenant setup).
     */
    public static void ensureCfrSchemaExists() {
        try {
            JdbcTemplate jdbcTemplate = createJdbcTemplate();
            jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS CFR");
            log.info("Ensured CFR schema exists");
        } catch (Exception e) {
            log.warn("Failed to ensure CFR schema exists: {}", e.getMessage());
        }
    }
    
    /**
     * Creates a JdbcTemplate connected to the shared container.
     */
    private static JdbcTemplate createJdbcTemplate() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl(SharedPostgreSQLContainer.getJdbcUrl());
        dataSource.setUsername(SharedPostgreSQLContainer.getUsername());
        dataSource.setPassword(SharedPostgreSQLContainer.getPassword());
        return new JdbcTemplate(dataSource);
    }

}
