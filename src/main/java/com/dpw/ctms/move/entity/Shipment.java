package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.ShipmentStatus;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "shipment")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Shipment extends BaseEntity {

    @Column(name = "code")
    private String code;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ShipmentStatus status;

    @Column(name = "external_current_location_code")
    private String externalCurrentLocationCode;

    @Column(name = "expected_pickup_at")
    private Long expectedPickupAt;

    @Column(name = "expected_delivery_at")
    private Long expectedDeliveryAt;

    @Column(name = "actual_pickup_at")
    private Long actualPickupAt;

    @Column(name = "actual_delivery_at")
    private Long actualDeliveryAt;

    @Column(name = "external_consignment_id")
    private String externalConsignmentId;

    @Column(name = "external_customer_order_id")
    private String externalCustomerOrderId;

    @Column(name = "volume")
    private BigDecimal volume;

    @Column(name = "volume_uom")
    private String volumeUom;

    @Column(name = "weight")
    private BigDecimal weight;

    @Column(name = "weight_uom")
    private String weightUom;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "loading_details", columnDefinition = "jsonb")
    private JsonNode loadingDetails;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "unloading_details", columnDefinition = "jsonb")
    private JsonNode unloadingDetails;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trip_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private Trip trip;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transport_order_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private TransportOrder transportOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "origin_stop_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private Stop originStop;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destination_stop_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private Stop destinationStop;

    @OneToMany(mappedBy = "shipment", cascade = CascadeType.ALL)
    @Builder.Default
    private List<ShipmentTask> shipmentTasks = new ArrayList<>();
}