package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.EventType;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "event")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Event extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private EventType type;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;
}
