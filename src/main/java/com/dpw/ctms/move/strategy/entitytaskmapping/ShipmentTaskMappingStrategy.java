package com.dpw.ctms.move.strategy.entitytaskmapping;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.ShipmentTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TaskParamType;
import org.springframework.stereotype.Component;

@Component
public class ShipmentTaskMappingStrategy implements IEntityTaskMappingStrategy{
    @Override
    public void mapEntityTask(ParamValueBaseDTO paramValueDTO, Trip trip, Task task) {
        ParamValueShipmentDTO shipmentDTO = (ParamValueShipmentDTO) paramValueDTO;
        for (Shipment shipment : trip.getShipments()) {
            if (isTaskShipmentMappingPresent(shipmentDTO, shipment)) {
                ShipmentTask shipmentTask = createShipmentTask(shipment, task, shipmentDTO);
                createShipmentTaskBackreferences(task, shipment, shipmentTask);
            }
        }
    }

    @Override
    public TaskParamType getParamType() {
        return TaskParamType.SHIPMENT;
    }

    private boolean isTaskShipmentMappingPresent(ParamValueShipmentDTO dto, Shipment shipment) {
        return shipment != null && dto.getCode().equals(shipment.getCode());
    }

    private ShipmentTask createShipmentTask(Shipment shipment, Task task, ParamValueShipmentDTO dto) {
        return ShipmentTask.builder()
                .shipment(shipment)
                .task(task)
                .build();
    }

    private void createShipmentTaskBackreferences(Task task, Shipment shipment, ShipmentTask shipmentTask) {
        task.setShipmentTask(shipmentTask);
        shipment.getShipmentTasks().add(shipmentTask);
    }
}
