package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.TaskStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
@Builder
public class TripTasksDetailsDTO {
    private String code;
    private String externalTaskRegistrationCode;
    private String externalTaskMasterCode;
    private Integer sequence;
    private TaskStatus status;
    private Long expectedStartAt;
    private Long expectedEndAt;
    private Long actualStartAt;
    private Long actualEndAt;
    private Long updatedAt;
}
