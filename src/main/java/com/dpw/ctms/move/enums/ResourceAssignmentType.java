package com.dpw.ctms.move.enums;

public enum ResourceAssignmentType implements DisplayableStatusEnum {
    COST_CENTER("Internal"),
    VENDOR("External");

    private String displayText;

    ResourceAssignmentType(String displayText) {
        this.displayText = displayText;
    }

    @Override
    public String getDisplayName() {
        return this.displayText;
    }

    public static ResourceAssignmentType fromDisplayName(String displayText) {
        if (displayText == null) {
            return null;
        }
        for (ResourceAssignmentType status : ResourceAssignmentType.values()) {
            if (status.displayText.equalsIgnoreCase(displayText)) {
                return status;
            }
        }
        return null;
    }
}
