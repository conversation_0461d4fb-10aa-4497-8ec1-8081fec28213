package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.request.common.Pagination;
import org.springframework.data.domain.Page;

public interface ITripDataService {
    Trip getTripByCode(String tripCode);

    Trip getTripByCodeWithAllDetails(String tripCode);

    Page<Stop> getTripStopByTripCode(String tripCode, Pagination paginationRequestSo);

    Long getTotalStopCount(String tripCode);
}
