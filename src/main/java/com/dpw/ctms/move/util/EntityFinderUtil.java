package com.dpw.ctms.move.util;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

@Component
public class EntityFinderUtil {
    public <T> T getEntityByCode(List<T> items, String code, Function<T, String> codeExtractor,
                                 Supplier<? extends RuntimeException> exceptionSupplier) {
        return items.stream()
                .filter(item-> code.equals(codeExtractor.apply(item)))
                .findFirst()
                .orElseThrow(exceptionSupplier);
    }
}
