package com.dpw.ctms.move.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.AssertTrue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DateRange {
    
    private Long from;
    private Long to;
    
    @JsonIgnore
    @AssertTrue(message = "From date must be less than or equal to To date")
    public boolean isValidRange() {
        if (from == null || to == null) {
            return true; // Allow partial ranges
        }
        return from <= to;
    }
}
