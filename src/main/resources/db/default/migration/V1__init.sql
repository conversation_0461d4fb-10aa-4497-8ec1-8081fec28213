-- Create revinfo table first to avoid FK errors in audit tables
CREATE TABLE IF NOT EXISTS revinfo (
   rev BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
   revtstmp BIGINT
);
-- attachment table
CREATE TABLE IF NOT EXISTS attachment (
      id BIGSERIAL PRIMARY KEY,
      type <PERSON><PERSON><PERSON><PERSON>,
      size SMALLINT,
      name <PERSON><PERSON><PERSON><PERSON>,
      path VARCHAR,
      external_file_identifier UUID,
      created_at BIGINT NOT NULL,
      updated_at BIGINT,
      created_by VARCHAR NOT NULL,
      updated_by VARCHAR
 );

 CREATE TABLE IF NOT EXISTS attachment_aud (
     id BIGSERIAL,
     rev BIGINT NOT NULL,
     revtype SMALLINT,
     type <PERSON><PERSON><PERSON><PERSON>,
     size SMALLINT,
     name VARCHAR,
     path VARCHAR,
     external_file_identifier UUID,
     created_at BIGINT,
     updated_at BIGINT,
     created_by VARCHAR,
     updated_by VARCHA<PERSON>,
     CONSTRAINT attachment_aud_pkey PRIMARY KEY (id, rev),
     CONSTRAINT attachment_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
 );


CREATE TABLE IF NOT EXISTS event (
      id BIGSERIAL PRIMARY KEY,
      code VARCHAR UNIQUE,
      type VARCHAR,
      details JSONB,
      created_at BIGINT NOT NULL,
      updated_at BIGINT,
      created_by VARCHAR NOT NULL,
      updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS event_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    code VARCHAR,
    type VARCHAR,
    details JSONB,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT event_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT event_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);

CREATE TABLE IF NOT EXISTS exception (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    type VARCHAR,
    raised_by VARCHAR,
    raised_at BIGINT,
    external_task_definition_id VARCHAR,
    external_task_instance_id VARCHAR,
    details JSONB,
    trip_id BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS exception_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    code VARCHAR,
    type VARCHAR,
    raised_by VARCHAR,
    raised_at BIGINT,
    external_task_definition_id VARCHAR,
    external_task_instance_id VARCHAR,
    details JSONB,
    trip_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT exception_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT exception_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS exception_task_mapping (
    id BIGSERIAL PRIMARY KEY,
    exception_id BIGINT UNIQUE,
    task_id BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS exception_task_mapping_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    exception_id BIGINT,
    task_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT exception_task_mapping_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT exception_task_mapping_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS shipment (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR,
    status VARCHAR,
    external_current_location_code VARCHAR,
    expected_pickup_at BIGINT,
    expected_delivery_at BIGINT,
    actual_pickup_at BIGINT,
    actual_delivery_at BIGINT,
    external_consignment_id VARCHAR,
    external_customer_order_id VARCHAR,
    volume DECIMAL,
    volume_uom VARCHAR,
    weight DECIMAL,
    weight_uom VARCHAR,
    loading_details JSONB,
    unloading_details JSONB,
    trip_id BIGINT,
    transport_order_id BIGINT,
    origin_stop_id BIGINT,
    destination_stop_id BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS shipment_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    code VARCHAR,
    status VARCHAR,
    external_current_location_code VARCHAR,
    expected_pickup_at BIGINT,
    expected_delivery_at BIGINT,
    actual_pickup_at BIGINT,
    actual_delivery_at BIGINT,
    external_consignment_id VARCHAR,
    external_customer_order_id VARCHAR,
    volume DECIMAL,
    volume_uom VARCHAR,
    weight DECIMAL,
    weight_uom VARCHAR,
    loading_details JSONB,
    unloading_details JSONB,
    trip_id BIGINT,
    transport_order_id BIGINT,
    origin_stop_id BIGINT,
    destination_stop_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT shipment_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT shipment_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);

CREATE TABLE IF NOT EXISTS stop (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    external_location_code VARCHAR,
    sequence SMALLINT,
    trip_id BIGINT,
    details JSONB,
    status VARCHAR,
    segment_details JSONB,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS stop_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    code VARCHAR,
    status VARCHAR,
    external_location_code VARCHAR,
    sequence SMALLINT,
    trip_id BIGINT,
    details JSONB,
    segment_details JSONB,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT stop_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT stop_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS stop_task (
    id BIGSERIAL PRIMARY KEY,
    stop_id BIGINT,
    task_id BIGINT UNIQUE,
    task_event VARCHAR,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS stop_task_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    stop_id BIGINT,
    task_id BIGINT,
    task_event VARCHAR,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT stop_task_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT stop_task_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);

CREATE TABLE IF NOT EXISTS shipment_task (
    id BIGSERIAL PRIMARY KEY,
    shipment_id BIGINT,
    task_id BIGINT UNIQUE,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS shipment_task_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    shipment_id BIGINT,
    task_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT shipment_task_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT shipment_task_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS task (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    status VARCHAR,
    sequence INTEGER,
    expected_start_at BIGINT,
    expected_end_at BIGINT,
    actual_start_at BIGINT,
    actual_end_at BIGINT,
    external_task_master_code VARCHAR,
    external_task_registration_code VARCHAR,
    details JSONB,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS task_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    code VARCHAR,
    status VARCHAR,
    sequence INTEGER,
    expected_start_at BIGINT,
    expected_end_at BIGINT,
    actual_start_at BIGINT,
    actual_end_at BIGINT,
    external_task_code VARCHAR,
    external_task_master_code VARCHAR,
    external_task_registration_code VARCHAR,
    details JSONB,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT task_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT task_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);

CREATE TABLE IF NOT EXISTS task_param (
    id BIGSERIAL PRIMARY KEY,
    param_name VARCHAR,
    param_value JSONB,
    task_id BIGINT REFERENCES task(id),
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);


CREATE TABLE IF NOT EXISTS task_param_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    param_name VARCHAR,
    param_value JSONB,
    task_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT task_param_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT task_param_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS trailer_resource (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    external_resource_id VARCHAR,
    resource_assignment_details JSONB,
    details JSONB,
    trip_id BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS trailer_resource_aud (
    id BIGSERIAL,
    code VARCHAR,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    external_resource_id VARCHAR,
    resource_assignment_details JSONB,
    details JSONB,
    trip_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT trailer_resource_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT trailer_resource_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS transport_order (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    status VARCHAR,
    assignment_type VARCHAR,
    assignment_code VARCHAR,
    assignee_identifier VARCHAR,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS transport_order_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    code VARCHAR,
    status VARCHAR,
    assignment_type VARCHAR,
    assignment_code VARCHAR,
    assignee_identifier VARCHAR,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT transport_order_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT transport_order_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS trip (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    status VARCHAR,
    external_origin_location_code VARCHAR,
    external_destination_location_code VARCHAR,
    expected_start_at BIGINT,
    expected_end_at BIGINT,
    actual_start_at BIGINT,
    actual_end_at BIGINT,
    details JSONB,
    transport_order_id BIGINT,
    expected_pickup_at BIGINT,
    expected_delivery_at BIGINT,
    actual_pickup_at BIGINT,
    actual_delivery_at BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS trip_aud (
    id BIGSERIAL,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    code VARCHAR,
    status VARCHAR,
    external_origin_location_code VARCHAR,
    external_destination_location_code VARCHAR,
    expected_start_at BIGINT,
    expected_end_at BIGINT,
    actual_start_at BIGINT,
    actual_end_at BIGINT,
    details JSONB,
    transport_order_id BIGINT,
    expected_pickup_at BIGINT,
    expected_delivery_at BIGINT,
    actual_pickup_at BIGINT,
    actual_delivery_at BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT trip_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT trip_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS vehicle_operator_resource (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    external_resource_id VARCHAR,
    resource_assignment_details JSONB,
    details JSONB,
    trip_id BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS vehicle_operator_resource_aud (
    id BIGSERIAL,
    code VARCHAR,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    external_resource_id VARCHAR,
    resource_assignment_details JSONB,
    details JSONB,
    trip_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT vehicle_operator_resource_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT vehicle_operator_resource_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);


CREATE TABLE IF NOT EXISTS vehicle_resource (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR UNIQUE,
    external_resource_id VARCHAR,
    resource_assignment_details JSONB,
    registration_number VARCHAR,
    external_vehicle_type_id VARCHAR,
    details JSONB,
    trip_id BIGINT UNIQUE,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR
);

CREATE TABLE IF NOT EXISTS vehicle_resource_aud (
    id BIGSERIAL,
    code VARCHAR,
    rev BIGINT NOT NULL,
    revtype SMALLINT,
    external_resource_id VARCHAR,
    resource_assignment_details JSONB,
    registration_number VARCHAR,
    external_vehicle_type_id VARCHAR,
    details JSONB,
    trip_id BIGINT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by VARCHAR,
    updated_by VARCHAR,
    CONSTRAINT vehicle_resource_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT vehicle_resource_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);









