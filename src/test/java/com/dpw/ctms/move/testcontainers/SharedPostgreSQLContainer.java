package com.dpw.ctms.move.testcontainers;

import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

import java.util.Map;

/**
 * Singleton PostgreSQL container for integration tests.
 * This ensures all integration tests share the same database container,
 * preventing container conflicts and improving test performance.
 */
public class SharedPostgreSQLContainer {
    
    private static final String POSTGRES_IMAGE = "postgres:15.0";
    private static final String DATABASE_NAME = "test_db";
    private static final String USERNAME = "test_user";
    private static final String PASSWORD = "test_password";
    
    private static PostgreSQLContainer<?> container;
    
    /**
     * Gets the singleton PostgreSQL container instance.
     * Creates and starts the container if it doesn't exist.
     * 
     * @return the shared PostgreSQL container
     */
    public static PostgreSQLContainer<?> getInstance() {
        if (container == null) {
            synchronized (SharedPostgreSQLContainer.class) {
                if (container == null) {
                    container = createContainer();
                    container.start();
                    
                    // Add shutdown hook to ensure container is stopped when <PERSON>V<PERSON> exits
                    Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                        if (container != null && container.isRunning()) {
                            container.stop();
                        }
                    }));
                }
            }
        }
        return container;
    }
    
    /**
     * Creates a new PostgreSQL container with optimized settings for testing.
     */
    private static PostgreSQLContainer<?> createContainer() {
        return new PostgreSQLContainer<>(DockerImageName.parse(POSTGRES_IMAGE))
                .withDatabaseName(DATABASE_NAME)
                .withUsername(USERNAME)
                .withPassword(PASSWORD)
                .withReuse(false) // Don't reuse between different test runs
                .withTmpFs(Map.of("/var/lib/postgresql/data", "rw")) // Use tmpfs for better performance
                .withCommand("postgres", 
                    "-c", "fsync=off", // Disable fsync for faster writes in tests
                    "-c", "synchronous_commit=off", // Disable synchronous commit
                    "-c", "full_page_writes=off", // Disable full page writes
                    "-c", "max_connections=100", // Limit connections
                    "-c", "shared_buffers=256MB", // Optimize memory
                    "-c", "log_statement=none" // Reduce logging
                );
    }
    
    /**
     * Gets the JDBC URL for the shared container.
     */
    public static String getJdbcUrl() {
        return getInstance().getJdbcUrl();
    }
    
    /**
     * Gets the username for the shared container.
     */
    public static String getUsername() {
        return getInstance().getUsername();
    }
    
    /**
     * Gets the password for the shared container.
     */
    public static String getPassword() {
        return getInstance().getPassword();
    }
    
    /**
     * Checks if the container is running.
     */
    public static boolean isRunning() {
        return container != null && container.isRunning();
    }
    
    /**
     * Stops the shared container (for cleanup in tests if needed).
     * Note: This should rarely be called as the container is shared.
     */
    public static void stop() {
        if (container != null && container.isRunning()) {
            container.stop();
            container = null;
        }
    }
}
