package com.dpw.ctms.move.statemachine.config;

import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.registry.IGuardRegistry;
import com.dpw.ctms.move.statemachine.registry.TaskGuardRegistry;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/** Mapping Task against guard registry **/

@Configuration
@RequiredArgsConstructor
public class GuardRegistryMapConfig {

    private final TaskGuardRegistry taskGuardRegistry;

    @Bean
    public Map<StateMachineEntityType, IGuardRegistry> guardRegistryMap() {
        Map<StateMachineEntityType, IGuardRegistry> map = new HashMap<>();
        map.put(StateMachineEntityType.TASK, taskGuardRegistry);
        return map;
    }
}
