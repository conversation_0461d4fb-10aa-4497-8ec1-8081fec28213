package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.TripApi;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.response.TripViewResponse;
import com.dpw.ctms.move.service.ITripService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequiredArgsConstructor
public class TripApiController implements TripApi {

    private final ITripService tripService;

    @Override
    public ResponseEntity<ListResponse<TripListingResponse>> listTrips(TripListingRequest tripListingRequest) {
        return ResponseEntity.ok(tripService.listTrips(tripListingRequest));
    }

    @Override
    public ResponseEntity<TripViewResponse> getTripView(String tripCode) {
        return new ResponseEntity<>(tripService.getTripView(tripCode), HttpStatus.OK);
    }
}
