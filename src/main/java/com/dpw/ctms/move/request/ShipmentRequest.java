package com.dpw.ctms.move.request;

import com.dpw.ctms.move.annotation.ValidEnum;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TaskParamType;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ShipmentRequest{
        @NotBlank
        @Schema(
                name = "code",
                description = "unique code of a shipment",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        String code;

        @Valid
        @NotNull
        @Schema(
                name = "originStop",
                description = "origin stop of a shipment",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        StopDetailsRequest originStop;

        @Valid
        @NotNull
        @Schema(
                name = "destinationStop",
                description = "destination stop of a shipment",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        StopDetailsRequest destinationStop;

        Long expectedPickupAt;

        Long expectedDeliveryAt;

        @NotBlank(message = "externalConsignmentId cannot be blank or null")
        String externalConsignmentId;

        String externalCustomerOrderId;

        @NotNull(message = "Shipment status cannot be null")
        @Schema(
                name = "status",
                description = "Denotes status of the shipment",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        @ValidEnum(enumClass = ShipmentStatus.class, message = "Invalid shipment status. Allowed values: {enumValues}")
        String status;

        @Positive(message = "Weight must be non-negative")
        BigDecimal weight;
        String weightUom;

        @Positive(message = "Volume must be non-negative")
        BigDecimal volume;
        String volumeUom;

        JsonNode loadingDetails;
        JsonNode unloadingDetails;
}
