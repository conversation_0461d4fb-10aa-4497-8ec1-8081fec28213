package com.dpw.ctms.move.registry;

import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.strategy.entitytaskmapping.IEntityTaskMappingStrategy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class EntityTaskMappingStrategyRegistry {
    private final Map<TaskParamType, IEntityTaskMappingStrategy> entityTaskMappingStrategyMap = new HashMap<>();
    public EntityTaskMappingStrategyRegistry(List<IEntityTaskMappingStrategy> entityTaskMappingStrategies) {
        for (IEntityTaskMappingStrategy entityTaskMappingStrategy: entityTaskMappingStrategies) {
            entityTaskMappingStrategyMap.put(entityTaskMappingStrategy.getParamType(),
                    entityTaskMappingStrategy);
        }
    }
    public IEntityTaskMappingStrategy getEntityTaskMappingStrategy(TaskParamType taskParamType) {

        return entityTaskMappingStrategyMap.get(taskParamType);
    }
}
