package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.response.TripViewResponse;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.tmsutils.threadlocal.TenantContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class TripViewControllerIntegrationTest extends IntegrationTestBase {

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private TransportOrderRepository transportOrderRepository;

    private static final String TRIP_CODE = "TRIP-001";
    private static final String TRANSPORT_ORDER_CODE = "TO-001";
    private static final String STOP_CODE = "STOP-001";
    private static final String SHIPMENT_CODE = "SHIP-001";
    private static final String VEHICLE_CODE = "VEH-001";
    private static final String TRAILER_CODE = "TRAILER-001";
    private static final String OPERATOR_CODE = "OP-001";

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
        TestDatabaseManager.cleanupCfrSchema();
    }

    @AfterEach
    void cleanup() {
        TenantContext.setCurrentTenant("CFR");
        try {
            tripRepository.deleteAll();
            transportOrderRepository.deleteAll();
        } catch (java.lang.Exception e) {
            TestDatabaseManager.cleanupCfrSchema();
        }
        TenantContext.clear();
    }

    @Test
    @Transactional
    void getTripView_ShouldReturnTripWithAllRelations() throws java.lang.Exception {
        // Given
        setupTestData();

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/trips/{tripCode}/view", TRIP_CODE)
                .contentType(MediaType.APPLICATION_JSON)
                .header(TENANT_HEADER, "CFR"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(TRIP_CODE))
                .andExpect(jsonPath("$.status.value").value(TripStatus.PLANNED.name()))
                .andExpect(jsonPath("$.status.label").value(TripStatus.PLANNED.getDisplayName()))
                .andExpect(jsonPath("$.transportOrder.code").value(TRANSPORT_ORDER_CODE))
                .andExpect(jsonPath("$.stops[0].code").value(STOP_CODE))
                .andExpect(jsonPath("$.shipments[0].code").value(SHIPMENT_CODE))
                .andExpect(jsonPath("$.resourceDetails.vehicleResource.externalResourceId").value(VEHICLE_CODE))
                .andExpect(jsonPath("$.resourceDetails.trailerResources[0].externalResourceId").value(TRAILER_CODE))
                .andExpect(jsonPath("$.resourceDetails.vehicleOperatorResources[0].externalResourceId").value(OPERATOR_CODE))
                .andReturn();

        // Parse response
        TripViewResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), TripViewResponse.class);
        assertNotNull(response);
        assertEquals(TRIP_CODE, response.getCode());
        assertEquals(TripStatus.PLANNED.getDisplayName(), response.getStatus().getLabel());
    }

    @Test
    void getTripView_WhenTripDoesNotExist_ShouldReturnNotFound() throws java.lang.Exception {
        // When & Then
        mockMvc.perform(post("/v1/trips/{tripCode}/view", "NON_EXISTENT_TRIP")
                .contentType(MediaType.APPLICATION_JSON)
                .header(TENANT_HEADER, "CFR"))
                .andExpect(status().isUnprocessableEntity())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode").value("INVALID_REQUEST"))
                .andExpect(jsonPath("$.message").value("Trip not found with code: NON_EXISTENT_TRIP"));
    }

    private void setupTestData() {

        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode(TRANSPORT_ORDER_CODE);
        transportOrder.setTrips(new ArrayList<>());
        transportOrderRepository.save(transportOrder);


        Trip trip = new Trip();
        trip.setCode(TRIP_CODE);
        trip.setStatus(TripStatus.PLANNED);
        trip.setTransportOrder(transportOrder);
        tripRepository.save(trip);


        Stop stop = new Stop();
        stop.setCode(STOP_CODE);
        trip.setStops(new HashSet<>());
        trip.getStops().add(stop);


        Shipment shipment = new Shipment();
        shipment.setCode(SHIPMENT_CODE);
        trip.setShipments(new HashSet<>());
        trip.getShipments().add(shipment);

        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId(VEHICLE_CODE);
        trip.setVehicleResource(vehicleResource);

        TrailerResource trailerResource = new TrailerResource();
        trailerResource.setExternalResourceId(TRAILER_CODE);
        trip.setTrailerResources(new HashSet<>());
        trip.getTrailerResources().add(trailerResource);


        VehicleOperatorResource operatorResource = new VehicleOperatorResource();
        operatorResource.setExternalResourceId(OPERATOR_CODE);
        trip.setVehicleOperatorResources(new HashSet<>());
        trip.getVehicleOperatorResources().add(operatorResource);

        tripRepository.save(trip);
    }
} 