package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.response.TripViewResponse;
import com.dpw.tmsutils.annotation.ApiLog;
import com.dpw.tmsutils.schemaobjects.TMSErrorResponseSo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/v1/trips")
public interface TripApi {

    @PostMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Trip listing")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully listed the trips"),
            @ApiResponse(responseCode = "422", description = "Unable to list the trips", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            }
            )
    })
    @ApiLog
    ResponseEntity<ListResponse<TripListingResponse>> listTrips(
            @Valid
            @RequestBody
            @Parameter(name = "TripListingRequest", required = true, description = "Request to list trips")
            TripListingRequest tripListingRequest);


    @PostMapping(value = "/{tripCode}/view", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get trip view details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully fetched trip view details"),
            @ApiResponse(responseCode = "422", description = "Unable to fetch trip view details", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<TripViewResponse> getTripView(
            @PathVariable("tripCode") @Parameter(name = "tripCode", required = true, description = "Trip code")
            @NotNull String tripCode);

}