package com.dpw.ctms.move.config;

import com.dpw.ctms.move.mapper.IEntityRelationshipMapper;
import com.dpw.ctms.move.mapper.ShipmentStopMapper;
import com.dpw.ctms.move.mapper.TaskDTOEntityMapper;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration
public class EntityRelationshipMapperConfig {
    public List<IEntityRelationshipMapper> relationshipMappers(
            TaskDTOEntityMapper taskDTOEntityMapper,
            ShipmentStopMapper shipmentStopMapper) {
        return Arrays.asList(taskDTOEntityMapper,shipmentStopMapper);
    }
}
