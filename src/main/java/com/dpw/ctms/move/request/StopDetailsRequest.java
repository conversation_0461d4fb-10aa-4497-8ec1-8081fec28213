package com.dpw.ctms.move.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StopDetailsRequest{
        @NotBlank(message = "externalLocationCode of a stop cannot be null")
        String externalLocationCode;

        @NotNull(message = "sequence of a stop cannot be null")
        Integer sequence;
}
