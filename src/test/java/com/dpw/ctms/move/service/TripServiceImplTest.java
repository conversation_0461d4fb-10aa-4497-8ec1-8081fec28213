package com.dpw.ctms.move.service;

import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.service.impl.TripServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TripServiceImplTest {

    @Mock
    private TripFilteringService tripFilteringService;

    @InjectMocks
    private TripServiceImpl tripService;

    private TripListingRequest tripListingRequest;
    private ListResponse<TripListingResponse> expectedResponse;

    @BeforeEach
    void setUp() {
        // Setup request
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripIds(Arrays.asList("TRIP001", "TRIP002"));
        filter.setTripStatuses(Arrays.asList("ACTIVE", "ENROUTE"));
        filter.setTransportOrderIds(Arrays.asList("TO001"));
        filter.setCustomerOrderIds(Arrays.asList("CO001"));
        filter.setVendorIds(Arrays.asList("VENDOR001"));
        filter.setConsignmentIds(Arrays.asList("CONSIGNMENT001"));
        filter.setTransportOrderStatuses(Arrays.asList("CONFIRMED"));
        filter.setShipmentStatuses(Arrays.asList("PLANNED"));
        filter.setExpectedPickupDateRange(Faker.createDateRange(1000L, 1200L));
        filter.setExpectedDeliveryDateRange(Faker.createDateRange(2000L, 2200L));
        filter.setActualPickupDateRange(Faker.createDateRange(1500L, 1700L));
        filter.setActualDeliveryDateRange(Faker.createDateRange(2500L, 2700L));

        tripListingRequest = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filter)
                .build();

        // Setup expected response
        List<TripListingResponse> tripList = new ArrayList<>();
        tripList.add(createSampleTripResponse("TRIP001", TripStatus.ACTIVE));
        tripList.add(createSampleTripResponse("TRIP002", TripStatus.ENROUTE));

        expectedResponse = ListResponse.<TripListingResponse>builder()
                .data(tripList)
                .totalRecords(2L)
                .build();
    }

    @Test
    void listTrips_ShouldDelegateToFilteringService() {
        // Arrange
        when(tripFilteringService.filterTrips(any(TripListingRequest.class))).thenReturn(expectedResponse);

        // Act
        ListResponse<TripListingResponse> response = tripService.listTrips(tripListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripFilteringService).filterTrips(tripListingRequest);
    }

    @Test
    void listTrips_WithNullFilter_ShouldDelegateToFilteringService() {
        // Arrange
        TripListingRequest requestWithNullFilter = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(null)
                .build();

        when(tripFilteringService.filterTrips(any(TripListingRequest.class))).thenReturn(expectedResponse);

        // Act
        ListResponse<TripListingResponse> response = tripService.listTrips(requestWithNullFilter);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(tripFilteringService).filterTrips(requestWithNullFilter);
    }

    @Test
    void listTrips_WithNullPaginationAndSort_ShouldDelegateToFilteringService() {
        // Arrange
        TripListingRequest requestWithNullPaginationAndSort = TripListingRequest.builder()
                .pagination(null)
                .sort(null)
                .filter(new TripListingRequest.Filter())
                .build();

        when(tripFilteringService.filterTrips(any(TripListingRequest.class))).thenReturn(expectedResponse);

        // Act
        ListResponse<TripListingResponse> response = tripService.listTrips(requestWithNullPaginationAndSort);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(tripFilteringService).filterTrips(requestWithNullPaginationAndSort);
    }

    @Test
    void listTrips_WithComplexFilter_ShouldDelegateToFilteringService() {
        // Arrange
        when(tripFilteringService.filterTrips(any(TripListingRequest.class))).thenReturn(expectedResponse);

        // Act
        ListResponse<TripListingResponse> response = tripService.listTrips(tripListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(tripFilteringService).filterTrips(tripListingRequest);
    }

    private TripListingResponse createSampleTripResponse(String tripCode, TripStatus status) {
        EnumLabelValueResponse statusInfo = new EnumLabelValueResponse(status.getDisplayName(), status.name());
        TripListingResponse.TimeRange expectedTimes = new TripListingResponse.TimeRange(1000L, 2000L);
        TripListingResponse.TimeRange actualTimes = new TripListingResponse.TimeRange(1500L, 2500L);
        
        return TripListingResponse.builder()
                .code(tripCode)
                .status(statusInfo)
                .transportOrderCode("TO-" + tripCode)
                .expectedTimes(expectedTimes)
                .actualTimes(actualTimes)
                .build();
    }
}