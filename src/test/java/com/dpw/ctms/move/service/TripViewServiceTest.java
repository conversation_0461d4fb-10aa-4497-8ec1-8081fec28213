package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.mapper.TripViewMapper;
import com.dpw.ctms.move.response.TripViewResponse;
import com.dpw.ctms.move.service.impl.TripServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashSet;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TripViewServiceTest {

    @Mock
    private ITripDataService tripDataService;

    @Mock
    private TripViewMapper tripViewMapper;

    @InjectMocks
    private TripServiceImpl tripService;

    private static final String TRIP_CODE = "TRIP-001";

    @Test
    void getTripView_WhenTripExists_ShouldReturnTripViewResponse() {
        Trip trip = new Trip();
        trip.setCode(TRIP_CODE);
        trip.setStatus(TripStatus.PLANNED);
        trip.setShipments(new HashSet<>());

        TripViewResponse expected = new TripViewResponse();
        expected.setCode(TRIP_CODE);

        when(tripDataService.getTripByCodeWithAllDetails(TRIP_CODE)).thenReturn(trip);
        when(tripViewMapper.toResponse(trip)).thenReturn(expected);

        TripViewResponse result = tripService.getTripView(TRIP_CODE);

        assertNotNull(result);
        assertEquals(TRIP_CODE, result.getCode());
        verify(tripDataService).getTripByCodeWithAllDetails(TRIP_CODE);
        verify(tripViewMapper).toResponse(trip);
    }

    @Test
    void getTripView_WhenTripDoesNotExist_ShouldThrowException() {
        when(tripDataService.getTripByCodeWithAllDetails(TRIP_CODE))
                .thenThrow(new TMSException(INVALID_REQUEST.name(),
                        "Trip not found with code: " + TRIP_CODE));

        TMSException ex = assertThrows(TMSException.class,
                () -> tripService.getTripView(TRIP_CODE));

        assertEquals(INVALID_REQUEST.name(), ex.getErrorCode());
        verify(tripDataService).getTripByCodeWithAllDetails(TRIP_CODE);
        verify(tripViewMapper, never()).toResponse(any());
    }
}