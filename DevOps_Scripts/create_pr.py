import git
import base64
import requests
import os
import shutil
import sys
import re
import time

organization = "https://dpwhotfsonline.visualstudio.com/"
token = sys.argv[1]
token_org = f"https://{token}@dpwhotfsonline.visualstudio.com/"
project_name = "TMS"
repo_name = sys.argv[2]
triggered_branch = sys.argv[3]
tag = sys.argv[4]
service_name=sys.argv[5]

branch_list = ["development", "qa", "staging", "prod","pt"]

# Maximum number of retry attempts
MAX_RETRIES = 5

# Delay between retry attempts in seconds
RETRY_DELAY = 5

# Decorator for adding retry logic to functions
def retry(max_retries=MAX_RETRIES, retry_delay=RETRY_DELAY):
    def decorator(api_function):
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    response = api_function(*args, **kwargs)
                    # Check if the API call was successful
                    if response is not None:
                        return response
                    # Increment the retry count and wait before the next attempt
                    retries += 1
                    if retries < max_retries:
                        print(f"Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                except Exception as e:
                    print(f"Error: {e}")
            return None
        return wrapper
    return decorator

@retry()
def clone_repo(project_name, repo_name):
    try:
        repo = git.Repo.clone_from(f'{token_org}/{project_name}/_git/{repo_name}', f'./{repo_name}')
        print(f'Repo - {repo_name} cloned successfully')
        return repo
    except Exception as e:
        print(f"Repo cloning is not successful {e}")
        return None

@retry()
def create_feature_branch(repo, source_branch, target_branch):
    try:
        repo.git.checkout(f'origin/{target_branch}')
        repo.git.checkout('-b', source_branch)
        print(f'Branch creation done - {source_branch}')
        return repo
    except Exception as e:
        print(f"Feature branch is not created {e}")
        return None

@retry()
def commit_and_push_changes(repo, source_branch, commit_message):
    try:
        repo.git.add('--all')
        repo.git.config('--global', 'user.email', '<EMAIL>')
        repo.git.config('--global', 'user.name', 'Devops Shared Account')
        repo.git.commit('-m', commit_message)
        print('committed the changes!')

        # Push the changes to the remote branch
        origin = repo.remote('origin')
        origin.push(source_branch)
        return repo
    except Exception as e:
        print(f"Changes are not pushed yet {e}")
        return None

def handle_api_response(response, success_status_code, function_name):
    if response.status_code == success_status_code:
        return response.json()
    else:
        print(f"Function: {function_name} -> Failed with status code {response.status_code}. Response content: {response.text}")
        return None

@retry()
def get_repo_id_by_name(project_name, repo_name):
    try:
        credentials = base64.b64encode(bytes(f":{token}", "utf-8")).decode("utf-8")
        headers = {
            "Authorization": f"Basic {credentials}",
            "Content-Type": "application/json"
        }
        repo_url = f"{organization}/{project_name}/_apis/git/repositories/{repo_name}?api-version=7.0"

        #Get Repo ID
        response = requests.get(repo_url, headers=headers)
        response = handle_api_response(response, 200, 'get_repo_id_by_name')
        if response['id']:
            return response['id']

        raise ValueError(f"Repo ID not found for {repo_name}")
    except Exception as e:
        print(f"Error in getting repo ID by name - {repo_name}: {e}")

@retry()
def create_pr(project_name, repo_name, source_branch_name, target_branch_name, pr_title):
    try:
        credentials = base64.b64encode(bytes(f":{token}", "utf-8")).decode("utf-8")
        headers = {
            "Authorization": f"Basic {credentials}",
            "Content-Type": "application/json"
        }
        repo_id = get_repo_id_by_name(project_name, repo_name)
        create_pr_url = f"{organization}/{project_name}/_apis/git/repositories/{repo_id}/pullrequests?api-version=7.0"

        payload = {
            "sourceRefName": f"refs/heads/{source_branch_name}",
            "targetRefName": f"refs/heads/{target_branch_name}",
            "title": f"{pr_title}"
        }

        response = requests.post(create_pr_url, json=payload, headers=headers)
        response = handle_api_response(response, 201, 'create_pr')
        if response['pullRequestId']:
            return [f"{organization}/{project_name}/_git/{repo_id}/pullrequest/{response['pullRequestId']}", response['pullRequestId']]
        return None
    except Exception as e:
        print(f"PR creation failed on from source branch: {source_branch_name} to target branch: {target_branch_name} - {e}")
        exit(1)

@retry()
def auto_merge_pr(pr_id):
    try:
        credentials = base64.b64encode(bytes(f":{token}", "utf-8")).decode("utf-8")
        headers = {
            "Authorization": f"Basic {credentials}",
            "Content-Type": "application/json"
        }

        repo_id = get_repo_id_by_name(project_name, repo_name)
        pr_url = f"{organization}/{project_name}/_apis/git/repositories/{repo_id}/pullrequests/{pr_id}?api-version=7.0"

        # get request to get the commit id
        response = requests.get(pr_url, headers=headers)
        response = handle_api_response(response, 200, 'get PR details')
        if response['lastMergeSourceCommit']:
            lastMergeSourceCommit = response['lastMergeSourceCommit']["commitId"]
        else:
            print("Error")
            raise Exception()
        
        payload = {
            "status": "completed",
            "lastMergeSourceCommit": {
                "commitId": f"{lastMergeSourceCommit}"
            },
            "completionOptions": {
                "bypassPolicy": "true",
                "bypassReason": "Bypassing for dev branch - auto merge"
            }
        }

        # patch request to merge the pr
        response = requests.patch(pr_url, json=payload, headers=headers)
        response = handle_api_response(response, 200, 'Auto merge PR to dev')
        print(response, "\n")
        if response['status']:
            time.sleep(10)

            #get request to get the status
            response = requests.get(pr_url, headers=headers)
            response = handle_api_response(response, 200, 'get PR details')

            if response['status'] == 'completed':
                return f"**** Auto merge of PR {pr_id} is successful ****"
            elif response['status'] == 'active' and response['mergeStatus'] == 'conflicts':
                print(f"**** Auto merge failed due to conflicts in the PR - {pr_id} ****")
                return None
        return None
    except Exception as e:
        print(f"Auto merge failed for this PR {pr_id} due to some API issue")
        exit(1)


def update_file(file_path):
    try:
        with open(file_path, 'r', encoding="utf-8") as file:
            lines = file.readlines()

        with open(file_path, 'w', encoding="utf-8") as file:
            for line in lines:
                if "kubernetes.io/change-cause:" in line:
                    line = re.sub(r"kubernetes.io/change-cause:.*", f"kubernetes.io/change-cause: {tag}", line)
                elif "acrcargoestmsnonprod.azurecr.io/ctms-move:" in line:
                    line = re.sub(r"acrcargoestmsnonprod.azurecr.io/ctms-move:.*", f"acrcargoestmsnonprod.azurecr.io/ctms-move:{tag}", line)
                file.write(line)

    except UnicodeDecodeError:
        # Handle the error as needed
        print(f"Error reading file '{file_path}'")

def update_image_tag():
    branch = triggered_branch
    repo = clone_repo(project_name, repo_name)

    source_branch = f'feature/{service_name}-{branch}-{tag}'

    if branch not in branch_list:
        branch = "development"

    repo.git.checkout(f'origin/{branch}')
    repo = create_feature_branch(repo, source_branch, branch)
    file_path = os.path.join(repo.working_dir, f"{service_name}/k8s/deployment.yaml")
    # Check if the file exists
    if os.path.isfile(file_path):
        print(file_path)
        update_file(file_path)
    else:
        print(f"The file {file_path} does not exist. No action needed.")
        exit(1)
    
    print("Committing the changes")
    commit_message = f'updated image tag'
    repo = commit_and_push_changes(repo, source_branch, commit_message)
    print("changes pushed.....")
    # raising the PR
    pr_title = f'{service_name} {branch} {tag}'
    pr_creation_res = create_pr(project_name, repo_name, source_branch, branch, pr_title)
    if pr_creation_res:
        print("PR created successfully")
        print(pr_creation_res[0])

        print(f"*************** Auto Merge Triggered on {branch} branch ***************")
        auto_merge_res = auto_merge_pr(pr_creation_res[1])
        if auto_merge_res:
            print(auto_merge_res)
        else:
            print(f"*** Auto merge PR failed to {branch} branch, please check the active PR ***")
            exit(1)
    else:
        print(f"PR creation failed on repo: {repo_name}, Source Branch name: {source_branch}, target branch name: {branch}")
        exit(1)

    shutil.rmtree(f'./{repo_name}')
    print(f"deleted repo - {repo_name}")

update_image_tag()