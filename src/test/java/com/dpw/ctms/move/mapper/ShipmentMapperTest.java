package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import static org.junit.jupiter.api.Assertions.*;

public class ShipmentMapperTest {

    private ShipmentMapper shipmentMapper;
    private Shipment shipment;

    @BeforeEach
    void setUp() {
        shipmentMapper = Mappers.getMapper(ShipmentMapper.class);
        shipment = createCompleteShipment();
    }

    @Test
    void toResponse_WithCompleteShipment_ShouldMapAllFields() {
        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertEquals("TRIP001", response.getTripCode());
        assertEquals("CO001", response.getCustomerOrderId());
        assertEquals("CONSIGNMENT001", response.getConsignmentId());
        assertEquals("TO001", response.getTransportOrderCode());
        
        // Verify status mapping
        assertNotNull(response.getStatus());
        assertEquals("Assigned", response.getStatus().getLabel());
        assertEquals("ASSIGNED", response.getStatus().getValue());
        
        // Verify timestamps
        assertEquals(5000L, response.getUpdatedAt());
        
        // Verify expected times
        assertNotNull(response.getExpectedTimes());
        assertEquals(1500L, response.getExpectedTimes().getStartAt());
        assertEquals(1800L, response.getExpectedTimes().getEndAt());
        
        // Verify actual times
        assertNotNull(response.getActualTimes());
        assertEquals(1600L, response.getActualTimes().getStartAt());
        assertEquals(1900L, response.getActualTimes().getEndAt());
        
        // Verify origin stop
        assertNotNull(response.getOriginStop());
        assertEquals("1", response.getOriginStop().getId());
        assertEquals("STOP001", response.getOriginStop().getCode());
        assertEquals("ORIGIN001", response.getOriginStop().getExternalLocationCode());
        assertNotNull(response.getOriginStop().getStatus());
        assertEquals("PLANNED", response.getOriginStop().getStatus().getValue());
        assertEquals(4000L, response.getOriginStop().getUpdatedAt());
        
        // Verify destination stop
        assertNotNull(response.getDestinationStop());
        assertEquals("2", response.getDestinationStop().getId());
        assertEquals("STOP002", response.getDestinationStop().getCode());
        assertEquals("DEST001", response.getDestinationStop().getExternalLocationCode());
        assertNotNull(response.getDestinationStop().getStatus());
        assertEquals("PLANNED", response.getDestinationStop().getStatus().getValue());
        assertEquals(4500L, response.getDestinationStop().getUpdatedAt());
    }

    @Test
    void toResponse_WithNullShipment_ShouldReturnNull() {
        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(null);

        // Assert
        assertNull(response);
    }

    @Test
    void toResponse_WithMinimalShipment_ShouldHandleNullFields() {
        // Arrange
        Shipment minimalShipment = new Shipment();
        minimalShipment.setCode("SHIP_MINIMAL");

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(minimalShipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP_MINIMAL", response.getCode());
        assertNull(response.getTripCode());
        assertNull(response.getCustomerOrderId());
        assertNull(response.getConsignmentId());
        assertNull(response.getTransportOrderCode());
        assertNull(response.getStatus());
        assertNull(response.getUpdatedAt());
        
        // Verify time ranges are created but with null values
        assertNotNull(response.getExpectedTimes());
        assertNull(response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());
        
        assertNotNull(response.getActualTimes());
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
        
        assertNull(response.getOriginStop());
        assertNull(response.getDestinationStop());
    }

    @Test
    void toResponse_WithNullStatus_ShouldHandleGracefully() {
        // Arrange
        shipment.setStatus(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getStatus());
    }

    @Test
    void toResponse_WithNullTrip_ShouldHandleGracefully() {
        // Arrange
        shipment.setTrip(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getTripCode());
    }

    @Test
    void toResponse_WithNullTransportOrder_ShouldHandleGracefully() {
        // Arrange
        shipment.setTransportOrder(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getTransportOrderCode());
    }

    @Test
    void toResponse_WithNullStops_ShouldHandleGracefully() {
        // Arrange
        shipment.setOriginStop(null);
        shipment.setDestinationStop(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getOriginStop());
        assertNull(response.getDestinationStop());
    }

    @Test
    void toResponse_WithNullTimestamps_ShouldHandleGracefully() {
        // Arrange
        shipment.setExpectedPickupAt(null);
        shipment.setExpectedDeliveryAt(null);
        shipment.setActualPickupAt(null);
        shipment.setActualDeliveryAt(null);
        shipment.setUpdatedAt(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getUpdatedAt());
        
        // Verify time ranges are created but with null values
        assertNotNull(response.getExpectedTimes());
        assertNull(response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());
        
        assertNotNull(response.getActualTimes());
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
    }

    @Test
    void mapStatusInfo_WithValidEnum_ShouldMapCorrectly() {
        // Act
        EnumLabelValueResponse result = shipmentMapper.mapStatusInfo(ShipmentStatus.ASSIGNED);

        // Assert
        assertNotNull(result);
        assertEquals("Assigned", result.getLabel());
        assertEquals("ASSIGNED", result.getValue());
    }

    @Test
    void mapStatusInfo_WithNullEnum_ShouldReturnNull() {
        // Act
        EnumLabelValueResponse result = shipmentMapper.mapStatusInfo(null);

        // Assert
        assertNull(result);
    }

    @Test
    void mapShipmentExpectedTimes_WithValidTimestamps_ShouldMapCorrectly() {
        // Act
        ShipmentListingResponse.TimeRange result = shipmentMapper.mapShipmentExpectedTimes(shipment);

        // Assert
        assertNotNull(result);
        assertEquals(1500L, result.getStartAt());
        assertEquals(1800L, result.getEndAt());
    }

    @Test
    void mapShipmentActualTimes_WithValidTimestamps_ShouldMapCorrectly() {
        // Act
        ShipmentListingResponse.TimeRange result = shipmentMapper.mapShipmentActualTimes(shipment);

        // Assert
        assertNotNull(result);
        assertEquals(1600L, result.getStartAt());
        assertEquals(1900L, result.getEndAt());
    }

    @Test
    void mapStop_WithValidStop_ShouldMapCorrectly() {
        // Arrange
        Stop stop = createCompleteStop();

        // Act
        ShipmentListingResponse.Stop result = shipmentMapper.mapStop(stop);

        // Assert
        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("STOP001", result.getCode());
        assertEquals("ORIGIN001", result.getExternalLocationCode());
        assertNotNull(result.getStatus());
        assertEquals("PLANNED", result.getStatus().getValue());
        assertEquals(4000L, result.getUpdatedAt());
    }

    @Test
    void mapStop_WithNullStop_ShouldReturnNull() {
        // Act
        ShipmentListingResponse.Stop result = shipmentMapper.mapStop(null);

        // Assert
        assertNull(result);
    }

    // Helper methods
    private Shipment createCompleteShipment() {
        Shipment shipment = new Shipment();
        shipment.setCode("SHIP001");
        shipment.setStatus(ShipmentStatus.ASSIGNED);
        shipment.setExternalCustomerOrderId("CO001");
        shipment.setExternalConsignmentId("CONSIGNMENT001");
        shipment.setExpectedPickupAt(1500L);
        shipment.setExpectedDeliveryAt(1800L);
        shipment.setActualPickupAt(1600L);
        shipment.setActualDeliveryAt(1900L);
        shipment.setUpdatedAt(5000L);

        // Add trip
        Trip trip = new Trip();
        trip.setCode("TRIP001");
        shipment.setTrip(trip);

        // Add transport order
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO001");
        shipment.setTransportOrder(transportOrder);

        // Add origin stop
        Stop originStop = createCompleteStop();
        shipment.setOriginStop(originStop);

        // Add destination stop
        Stop destinationStop = new Stop();
        destinationStop.setId(2L);
        destinationStop.setCode("STOP002");
        destinationStop.setExternalLocationCode("DEST001");
        destinationStop.setStatus(StopStatus.PLANNED);
        destinationStop.setUpdatedAt(4500L);
        shipment.setDestinationStop(destinationStop);

        return shipment;
    }

    private Stop createCompleteStop() {
        Stop stop = new Stop();
        stop.setId(1L);
        stop.setCode("STOP001");
        stop.setExternalLocationCode("ORIGIN001");
        stop.setStatus(StopStatus.PLANNED);
        stop.setUpdatedAt(4000L);
        return stop;
    }
}
