package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.TransportOrderApi;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.service.ITransportOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.CREATED;

@RequiredArgsConstructor
@RestController
@Validated
public class TransportOrderApiController implements TransportOrderApi {
    private final ITransportOrderService transportOrderService;
    @Override
    public ResponseEntity<TransportOrderResponse> createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        TransportOrderResponse transportOrderResponse = transportOrderService.createTransportOrderFTLFulfilment(transportOrderFTLCreateRequest);
        return ResponseEntity.status(CREATED).body(transportOrderResponse);
    }

}
