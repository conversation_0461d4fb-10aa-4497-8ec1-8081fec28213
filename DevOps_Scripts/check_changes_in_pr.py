import json
import os
import requests
import sys

# Azure DevOps variables
org = "https://dev.azure.com/dpwhotfsonline"
project = "TMS"
repository_id = "739cb486-4046-4924-ba83-e921046173e8"
commit_id = sys.argv[1]
token = sys.argv[2]

config_changes = False;

# Paths to check for changes
path_to_check = "/DevOps_Scripts/configs/"

# Initialize a session
headers = { "Content-Type": "application/json" }

# Get changes for the specific commit
api_url = f"{org}/{project}/_apis/git/repositories/{repository_id}/commits/{commit_id}/changes?api-version=7.0"
response = requests.get(api_url, auth=('', token), headers=headers)

if response.status_code == 200:
    changes = json.loads(response.text)
    changes = changes['changes']
    print(changes)

    count = 0

    # Check if there are changes in the specified paths
    for change in changes:
        if change['item']['gitObjectType'] == 'blob' and not change['item']['path'].startswith(path_to_check):
            count += 1 

    if count == 0:
        print("Commit has only config changes...")
        print(f"##vso[task.setvariable variable=config_changes]True")
    else:
        print("Commit has code changes too...")
        print("##vso[task.setvariable variable=config_changes]False")
else:
    print(f"Failed to fetch changes. Status Code: {response.status_code}, Response: {response.text}")