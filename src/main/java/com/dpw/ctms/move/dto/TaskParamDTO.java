package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.TaskParamType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;


@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskParamDTO {
    TaskParamType paramName;
    ParamValueBaseDTO paramValue;
}
