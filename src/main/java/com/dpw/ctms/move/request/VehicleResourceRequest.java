package com.dpw.ctms.move.request;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VehicleResourceRequest{

        @NotBlank(message = "Vehicle code cannot be null")
        String code;

        @NotBlank(message = "Vehicle resource id cannot be null")
        String externalResourceId;

        ResourceAssignmentDetailsRequest resourceAssignmentDetails;

        String registrationNumber;

        String externalVehicleTypeId;
        JsonNode details;
}
