package com.dpw.ctms.move.registry;

import com.dpw.ctms.move.enums.AssignmentStatus;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.AttachmentType;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.enums.ExceptionType;
import com.dpw.ctms.move.enums.ResourceAssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Registry that maps entity type strings to their corresponding enum classes.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EntityTypeEnumRegistry {

    /**
     * Map of entity type strings to their corresponding enum classes.
     * The keys are the entity type identifiers used in the API path,
     * and the values are the enum classes that contain the status values.
     */
    private static final Map<String, Class<? extends Enum<?>>> ENTITY_TYPE_ENUM_MAP = new HashMap<>();

    static {
        ENTITY_TYPE_ENUM_MAP.put("TRIP_STATUS", TripStatus.class);
        ENTITY_TYPE_ENUM_MAP.put("ASSIGNMENT_STATUS", AssignmentStatus.class);
        ENTITY_TYPE_ENUM_MAP.put("ASSIGNMENT_TYPE", AssignmentType.class);
        ENTITY_TYPE_ENUM_MAP.put("TASK_STATUS", TaskStatus.class);
        ENTITY_TYPE_ENUM_MAP.put("SHIPMENT_STATUS", ShipmentStatus.class);
        ENTITY_TYPE_ENUM_MAP.put("TRANSPORT_ORDER_STATUS", TransportOrderStatus.class);
        ENTITY_TYPE_ENUM_MAP.put("RESOURCE_ASSIGNMENT_TYPE", ResourceAssignmentType.class);
        ENTITY_TYPE_ENUM_MAP.put("ATTACHMENT_TYPE", AttachmentType.class);
    }

    public static Class<? extends Enum<?>> getEnumClass(String entityType) {
        if (entityType == null) {
            return null;
        }
        return ENTITY_TYPE_ENUM_MAP.get(entityType.toUpperCase());
    }

    public static boolean isSupported(String entityType) {
        if (entityType == null) {
            return false;
        }
        return ENTITY_TYPE_ENUM_MAP.containsKey(entityType.toUpperCase());
    }

    public static Set<String> getSupportedEntityTypes() {
        return ENTITY_TYPE_ENUM_MAP.keySet();
    }

}
