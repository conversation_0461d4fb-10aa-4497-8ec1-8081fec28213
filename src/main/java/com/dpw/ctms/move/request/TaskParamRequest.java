package com.dpw.ctms.move.request;

import com.dpw.ctms.move.annotation.ValidEnum;
import com.dpw.ctms.move.enums.TaskParamType;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskParamRequest {
    @ValidEnum(enumClass = TaskParamType.class, message = "Invalid stop task param type. Allowed values: {enumValues}")
    String paramName;
    JsonNode paramValue;
}
