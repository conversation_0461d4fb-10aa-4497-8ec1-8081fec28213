package com.dpw.ctms.move.kafka.consumer;

import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.PickupTaskMessageDTO;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@KafkaListener(
        groupId = "${kafka.consumer.shipment-task-acknowledgement.consumer-group-id:dpw-task-execution-results-dev}",
        topics = "${kafka.consumer.shipment-task-acknowledgement.topic:dpw-task-execution-results-dev}",
        containerFactory = "${kafka.consumer.shipment-task-acknowledgement.container-factory:shipmentTaskContainerFactory}",
        autoStartup = "${kafka.consumer.shipment-task-acknowledgement.enable:false}")
@Slf4j
@RequiredArgsConstructor
public class ShipmentTaskConsumer {

    private final TaskActionHandlerRegistry taskActionHandlerRegistry;

    @KafkaHandler
    public void handlePayload(ConsumerRecord<String, String> record) {
        try {
            String jsonMessage = record.value();
            log.debug("Received message from topic: {}, partition: {}, offset: {}",
                    record.topic(), record.partition(), record.offset());

            TypeReference<IntegratorMessageRequestDTO<PickupTaskMessageDTO>> typeRef = new TypeReference<>() {};
            IntegratorMessageRequestDTO<PickupTaskMessageDTO> message = ObjectMapperUtil.getObjectMapper()
                    .readValue(jsonMessage, typeRef);
            log.info("Received the Event from task service having event: {}", message);

            if (message == null) return;


//            cfr
//            securityContextUtils.setAuthenticationForQueueContext(oAuth2TokenService.generateAccessToken());
            String type = message.getMessage().getItem().getTaskDetail().getTaskName();
            TaskActionHandler handler = taskActionHandlerRegistry.getHandler(type);
            log.info("Dispatching action: {} to handler: {}", type, handler.getClass().getName());
            handler.handle(message);
        } catch (Exception e) {
            log.error("Error while processing message from topic: {}, partition: {}, offset: {}, message: {}",
                    record.topic(), record.partition(), record.offset(), record.value(), e);
        }
    }
}