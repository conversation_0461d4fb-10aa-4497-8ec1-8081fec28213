#! /bin/bash
#Install az devops 
az config set extension.use_dynamic_install=yes_without_prompt
echo $1 | az devops login
repository=$2
build_ID=$3
fail_count=0
release_version=`echo $build_ID | cut -d'_' -f1`

auto_Merge()
{
    az repos pr create --bypass-policy true	\
                   --bypass-policy-reason "Automerge Release PR Automation" \
                   --delete-source-branch false \
                   --description "Automerge Release PR" \
                   --merge-commit-message "Automerge Release PR - $release_version" \
                   --org https://dpwhotfsonline.visualstudio.com/  \
                   --project TMS \
                   --repository $repository \
                   --source-branch $release_version \
                   --target-branch $1 \
                   --title "Automerge Release PR - $release_version for $1 branch" > temp.txt
    if [[ $? -eq 0 ]]; then
        echo "*****PR Created Successfully on ${1} Branch*****"
        PR_ID=`grep -w "pullRequestId" temp.txt | cut -d":" -f2 | cut -d"," -f1`
    else
        echo "*****PR Creation Failed on ${1} Branch !!!*****"
        exit 1
    fi

    az repos pr update --id $PR_ID \
            --auto-complete true \
		    --status completed \
		    --org https://dpwhotfsonline.visualstudio.com/

    sleep 20
    az repos pr show --id $PR_ID --org https://dpwhotfsonline.visualstudio.com/ --query '{mergestatus:mergeStatus, pr_status:status}' > temp.txt
    merge_status=`cat temp.txt | jq -r ".mergestatus"`
    PR_status=`cat temp.txt | jq -r ".pr_status"`
    if [[ "$merge_status" == "succeeded" ]] && [[ "$PR_status" == "completed" ]]; then
        echo "*****Automerge of Release Branch - ${release_version} to ${1} Branch SUCCESS*****"
    elif [[ "$merge_status" == "conflicts" ]] && [[ "$PR_status" == "active" ]]; then
        echo "*****Automerge of Release Branch - ${release_version} to ${1} Branch FAILURE due to CONFLICTS*****"
        fail_count=$((fail_count+1))
    else
        echo "*****Automerge of Release Branch - ${release_version} to ${1} Branch FAILURE due to unknown reasons. Please check active PR*****"
        fail_count=$((fail_count+1))
    fi
}

#Automerge to Prod branch
auto_Merge master

#Create tag on Prod branch
create_tag()
{
    #Get Latest commit on master
    commit_ID=`az repos ref list --org https://dpwhotfsonline.visualstudio.com/  \
                   --project TMS \
                   --query "[?name=='refs/heads/master']" \
                   --repository $repository | grep -w 'objectId' | cut -d":" -f2 | cut -d'"' -f2`
    #Create tag on master
    az repos ref create --name tags/$release_version \
                        --object-id $commit_ID \
                        --org https://dpwhotfsonline.visualstudio.com/  \
                        --project TMS \
                        --repository $repository
    if [[ $? -eq 0 ]]; then
        echo "*****Release Tag Created Successfully*****"
    else
        echo "*****Release Tag Creation Failed !!!*****"
        exit 1
    fi
}
sleep 10
create_tag

#Additional PR's in case of Hotfix
if [[ `echo $build_ID | cut -d'.' -f3 | cut -d'_' -f1` -ne 0 ]]; then
    echo "*****This is Hotfix Release*****"
    major_version=`echo ${release_version} | cut -d"-" -f2 | cut -d"." -f1 | cut -c2`
    minor_version=`echo ${release_version} | cut -d"." -f2`
    minor_version=$((minor_version+1))
    next_Release="Release-v$major_version.$minor_version.0"

    #Automerge
    auto_Merge $next_Release
fi

if [[ fail_count -gt 0 ]];then
    echo "*****Automerge failed on some branch. Please check the log*****"
    exit 1
fi