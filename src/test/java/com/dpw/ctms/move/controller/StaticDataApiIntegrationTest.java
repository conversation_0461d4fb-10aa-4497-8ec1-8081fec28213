package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.StaticDataRequest;
import com.dpw.ctms.move.response.EntityStaticDataResponse;
import com.dpw.ctms.move.response.StaticDataResponse;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.util.List;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the Static Data API endpoint.
 * Tests the complete flow from API layer through service layer to verify
 * end-to-end functionality of the getStaticData method.
 */
@DisplayName("Static Data API Integration Tests")
class StaticDataApiIntegrationTest extends IntegrationTestBase {

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
    }

    @AfterEach
    void tearDown() {
        TenantContext.clear();
    }

    @Test
    @DisplayName("Should successfully return static data for basic valid entity types")
    void testGetStaticData_BasicValidEntityTypes_Success() throws Exception {
        // Given
        StaticDataRequest request = Faker.createBasicStaticDataRequest();

        // When
        MvcResult result = performStaticDataRequest(request);

        // Then
        StaticDataResponse response = parseResponse(result);
        assertThat(response).isNotNull();

        // Verify successful processing
        assertThat(response.getSuccessfulEntityTypes()).hasSize(2);
        assertThat(response.getSuccessfulEntityTypes()).containsExactlyInAnyOrder("TRIP_STATUS", "TASK_STATUS");
        assertThat(response.getFailedEntityTypes()).isEmpty();

        // Verify data structure
        assertThat(response.getData()).hasSize(2);
        assertThat(response.getData()).containsKeys("TRIP_STATUS", "TASK_STATUS");

        // Verify each entity type has valid data
        verifyEntityStaticData(response.getData().get("TRIP_STATUS"), "TRIP_STATUS");
        verifyEntityStaticData(response.getData().get("TASK_STATUS"), "TASK_STATUS");
    }

    @Test
    @DisplayName("Should successfully return static data for single entity type")
    void testGetStaticData_SingleEntityType_Success() throws Exception {
        // Given
        StaticDataRequest request = Faker.createSingleEntityTypeRequest("ASSIGNMENT_STATUS");

        // When
        MvcResult result = performStaticDataRequest(request);

        // Then
        StaticDataResponse response = parseResponse(result);
        assertThat(response).isNotNull();
        
        // Verify successful processing
        assertThat(response.getSuccessfulEntityTypes()).hasSize(1);
        assertThat(response.getSuccessfulEntityTypes()).contains("ASSIGNMENT_STATUS");
        assertThat(response.getFailedEntityTypes()).isEmpty();
        
        // Verify data structure
        assertThat(response.getData()).hasSize(1);
        assertThat(response.getData()).containsKey("ASSIGNMENT_STATUS");
        
        // Verify entity data
        EntityStaticDataResponse assignmentStatusData = response.getData().get("ASSIGNMENT_STATUS");
        verifyEntityStaticData(assignmentStatusData, "ASSIGNMENT_STATUS");
        
        // Verify specific enum values for ASSIGNMENT_STATUS
        List<String> optionValues = assignmentStatusData.getOptions().stream()
                .map(EntityStaticDataResponse.StatusOption::getValue)
                .toList();
        assertThat(optionValues).contains("PENDING", "ASSIGNED");
    }

    @Test
    @DisplayName("Should handle mixed valid and invalid entity types")
    void testGetStaticData_MixedValidInvalid_PartialSuccess() throws Exception {
        // Given
        StaticDataRequest request = Faker.createMixedValidInvalidEntityTypesRequest();

        // When
        MvcResult result = performStaticDataRequest(request);

        // Then
        StaticDataResponse response = parseResponse(result);
        assertThat(response).isNotNull();
        
        // Should have some successful and some failed
        assertThat(response.getSuccessfulEntityTypes()).isNotEmpty();
        assertThat(response.getFailedEntityTypes()).isNotEmpty();
        
        // Verify failed entity types have error details
        response.getFailedEntityTypes().forEach(error -> {
            assertThat(error.getEntityType()).isNotBlank();
            assertThat(error.getErrorCode()).isEqualTo("INVALID_REQUEST");
            assertThat(error.getErrorMessage()).contains("Unsupported entity type");
        });
        
        // Verify successful entity types have valid data
        response.getData().forEach((entityType, staticData) -> {
            verifyEntityStaticData(staticData, entityType);
        });
    }

    @Test
    @DisplayName("Should handle all invalid entity types")
    void testGetStaticData_AllInvalid_AllErrors() throws Exception {
        // Given
        StaticDataRequest request = Faker.createInvalidEntityTypesRequest();

        // When
        MvcResult result = performStaticDataRequest(request);

        // Then
        StaticDataResponse response = parseResponse(result);
        assertThat(response).isNotNull();
        
        // All should fail
        assertThat(response.getSuccessfulEntityTypes()).isEmpty();
        assertThat(response.getFailedEntityTypes()).isNotEmpty();
        assertThat(response.getData()).isEmpty();
        
        // Verify all failed entity types have error details
        response.getFailedEntityTypes().forEach(error -> {
            assertThat(error.getEntityType()).isNotBlank();
            assertThat(error.getErrorCode()).isEqualTo("INVALID_REQUEST");
            assertThat(error.getErrorMessage()).contains("Unsupported entity type");
        });
    }

    @Test
    @DisplayName("Should handle empty entity types list")
    void testGetStaticData_EmptyList_EmptyResponse() throws Exception {
        // Given
        StaticDataRequest request = Faker.createEmptyEntityTypesRequest();

        // When
        MvcResult result = performStaticDataRequest(request);

        // Then
        StaticDataResponse response = parseResponse(result);
        assertThat(response).isNotNull();
        
        // All should be empty
        assertThat(response.getSuccessfulEntityTypes()).isEmpty();
        assertThat(response.getFailedEntityTypes()).isEmpty();
        assertThat(response.getData()).isEmpty();
    }

    @Test
    @DisplayName("Should handle case insensitive entity types")
    void testGetStaticData_CaseInsensitive_NormalizedResults() throws Exception {
        // Given
        StaticDataRequest request = Faker.createCaseInsensitiveEntityTypesRequest();

        // When
        MvcResult result = performStaticDataRequest(request);

        // Then
        StaticDataResponse response = parseResponse(result);
        assertThat(response).isNotNull();
        
        // Should normalize to uppercase
        assertThat(response.getSuccessfulEntityTypes()).hasSize(4);
        assertThat(response.getSuccessfulEntityTypes()).containsExactlyInAnyOrder("TRIP_STATUS", "ASSIGNMENT_STATUS", "TASK_STATUS", "SHIPMENT_STATUS");
        assertThat(response.getFailedEntityTypes()).isEmpty();

        // Verify normalized entity types in data
        assertThat(response.getData()).hasSize(4);
        assertThat(response.getData()).containsKeys("TRIP_STATUS", "ASSIGNMENT_STATUS", "TASK_STATUS", "SHIPMENT_STATUS");
    }

    private MvcResult performStaticDataRequest(StaticDataRequest request) throws Exception {
        String requestBody = objectMapper.writeValueAsString(request);
        
        return mockMvc.perform(post("/v1/static")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR")
                        .content(requestBody))
                .andExpect(status().isOk())
                .andReturn();
    }

    private StaticDataResponse parseResponse(MvcResult result) throws Exception {
        String responseJson = result.getResponse().getContentAsString();
        return objectMapper.readValue(responseJson, new TypeReference<StaticDataResponse>() {});
    }

    private void verifyEntityStaticData(EntityStaticDataResponse staticData, String expectedEntityType) {
        assertThat(staticData).isNotNull();
        assertThat(staticData.getEntityType()).isEqualTo(expectedEntityType);
        assertThat(staticData.getOptions()).isNotEmpty();
        
        // Verify each option has required fields
        staticData.getOptions().forEach(option -> {
            assertThat(option.getLabel()).isNotBlank();
            assertThat(option.getValue()).isNotBlank();
        });
    }
}
