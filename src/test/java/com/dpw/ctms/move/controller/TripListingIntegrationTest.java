package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class TripListingIntegrationTest extends IntegrationTestBase {

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private TransportOrderRepository transportOrderRepository;

    private String uniqueId;

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
        uniqueId = String.valueOf(System.currentTimeMillis());
        cleanup();
        setupBasicTestData();
    }

    @AfterEach
    void cleanup() {
        TenantContext.setCurrentTenant("CFR");
        try {
            // Use repository cleanup first (safer)
            tripRepository.deleteAll();
            transportOrderRepository.deleteAll();
        } catch (Exception e) {
            // If repository cleanup fails, use database manager cleanup
            System.err.println("Repository cleanup failed, using database cleanup: " + e.getMessage());
            TestDatabaseManager.cleanupCfrSchema();
        }
        TenantContext.clear();
    }

    @Test
    void listTrips_WithNoFilters_ShouldReturnAllTrips() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest request = Faker.createBasicTripListingRequest();

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(4, response.getTotalRecords());
        assertEquals(4, response.getData().size());

        // Verify basic structure
        TripListingResponse firstTrip = response.getData().get(0);
        assertNotNull(firstTrip.getCode());
        assertNotNull(firstTrip.getStatus());
        assertNotNull(firstTrip.getTransportOrderCode());
    }

    @Test
    void listTrips_WithPagination_ShouldReturnPagedResults() throws Exception {
        // Arrange - Request page 0 with size 1
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest request = Faker.createTripListingRequestWithPagination(Faker.SINGLE_PAGE_PAGINATION);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(4, response.getTotalRecords()); // Total records (updated to 4)
        assertEquals(1, response.getData().size()); // Only 1 record on this page
    }

    @Test
    void listTrips_WithStatusFilter_ShouldReturnFilteredTrips() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = Faker.createTripStatusFilter(TripStatus.ACTIVE);
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords()); // 2 ACTIVE trips (1 basic + 1 complex)
        assertEquals(2, response.getData().size());
        assertEquals(TripStatus.ACTIVE.name(), response.getData().get(0).getStatus().getValue());
    }

    @Test
    void listTrips_WithEmptyResult_ShouldReturnEmptyList() throws Exception {
        // Arrange - Filter by valid status that doesn't match our test data
        // Our test data has ACTIVE and ENROUTE trips, so filter by COMPLETED
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = Faker.createTripStatusFilter(TripStatus.COMPLETED);
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
        assertTrue(response.getData().isEmpty());
    }

    @Test
    void listTrips_VerifyResponseStructure_ShouldContainAllFields() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest request = Faker.createSinglePageTripListingRequest();

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(4, response.getTotalRecords()); // Updated to expect 4 trips

        TripListingResponse trip = response.getData().get(0);

        // Verify main fields are present
        assertNotNull(trip.getCode());
        assertNotNull(trip.getStatus());
        assertNotNull(trip.getTransportOrderCode());

        // Verify status structure
        assertNotNull(trip.getStatus().getLabel());
        assertNotNull(trip.getStatus().getValue());
    }

    @Test
    void listTrips_WithTripIdsFilter_ShouldReturnOnlyMatchingTrips() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        String targetTripCode = "TRIP_BASIC_1_" + uniqueId;
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripIds(Arrays.asList(targetTripCode));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords(), "Should find exactly 1 trip with the specified ID");
        assertEquals(1, response.getData().size());
        assertEquals(targetTripCode, response.getData().get(0).getCode(), "Returned trip should match the filtered ID");
    }

    @Test
    void listTrips_WithNonExistentTripId_ShouldReturnEmptyResult() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripIds(Arrays.asList("NON_EXISTENT_TRIP_ID"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords(), "Should find no trips with non-existent ID");
        assertTrue(response.getData().isEmpty(), "Result list should be empty");
    }

    @Test
    void listTrips_WithTransportOrderIdFilter_ShouldReturnMatchingTrips() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        String targetTransportOrderCode = "TO_BASIC_" + uniqueId;
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTransportOrderIds(Arrays.asList(targetTransportOrderCode));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords(), "Should find 2 trips with the basic transport order");
        assertEquals(2, response.getData().size());
        // Verify all returned trips have the correct transport order
        response.getData().forEach(trip ->
            assertEquals(targetTransportOrderCode, trip.getTransportOrderCode(),
                "All returned trips should have the filtered transport order code"));
    }

    @Test
    void listTrips_WithTrailerIdsFilter_ShouldExecuteTrailerIdsSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTrailerIds(Arrays.asList("TRAILER_COMPLEX_1", "TRAILER_COMPLEX_2"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Should find trips with matching trailer IDs
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithVehicleOperatorIdsFilter_ShouldExecuteVehicleOperatorIdsSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setVehicleOperatorIds(Arrays.asList("DRIVER_COMPLEX_1", "DRIVER_COMPLEX_2"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Should find trips with matching vehicle operator IDs
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithOriginLocationIdFilter_ShouldReturnTripsFromSpecificOrigin() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        String targetOriginLocation = "ORIGIN_COMPLEX";
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setOriginLocationId(targetOriginLocation);
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords(), "Should find 1 trip with the specified origin location");
        assertEquals(1, response.getData().size());
        assertEquals(targetOriginLocation, response.getData().get(0).getLocations().getOrigin().getCode(),
            "Returned trip should have the filtered origin location");
    }

    @Test
    void listTrips_WithDestinationLocationIdFilter_ShouldReturnTripsToSpecificDestination() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        String targetDestinationLocation = "DEST_COMPLEX";
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setDestinationLocationId(targetDestinationLocation);
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords(), "Should find 1 trip with the specified destination location");
        assertEquals(1, response.getData().size());
        assertEquals(targetDestinationLocation, response.getData().get(0).getLocations().getDestination().getCode(),
            "Returned trip should have the filtered destination location");
    }

    @Test
    void listTrips_WithVehicleTypeFilter_ShouldReturnTripsWithMatchingVehicleType() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setVehicleTypes(Arrays.asList("TRUCK"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords(), "Should find 2 trips with TRUCK vehicle type");
        assertEquals(2, response.getData().size());
        // Verify all returned trips have vehicles of type TRUCK
        response.getData().forEach(trip -> {
            assertNotNull(trip.getResources(), "Trip should have resources");
            assertNotNull(trip.getResources().getVehicles(), "Trip should have vehicles");
            assertFalse(trip.getResources().getVehicles().isEmpty(), "Trip should have at least one vehicle");
            assertEquals("TRUCK", trip.getResources().getVehicles().get(0).getTypeCode(),
                "Vehicle should be of type TRUCK");
        });
    }

    @Test
    void listTrips_WithNonExistentVehicleType_ShouldReturnEmptyResult() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setVehicleTypes(Arrays.asList("AIRPLANE")); // Non-existent vehicle type
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords(), "Should find no trips with AIRPLANE vehicle type");
        assertTrue(response.getData().isEmpty(), "Result list should be empty");
    }

    @Test
    void listTrips_WithExpectedPickupDateRangeFilter_ShouldExecuteDateRangeSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedPickupDateRange(new DateRange(currentTime - 86400000L, currentTime + 86400000L)); // ±1 day
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithExpectedDeliveryDateRangeFilter_ShouldExecuteDateRangeSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedDeliveryDateRange(new DateRange(currentTime - 86400000L, currentTime + 86400000L)); // ±1 day
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithActualPickupDateRangeFilter_ShouldExecuteDateRangeSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualPickupDateRange(new DateRange(currentTime - 86400000L, currentTime + 86400000L)); // ±1 day
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithActualDeliveryDateRangeFilter_ShouldExecuteDateRangeSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualDeliveryDateRange(new DateRange(currentTime - 86400000L, currentTime + 86400000L)); // ±1 day
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithExpectedPickupDateFromOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only from date (>= condition)
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long fromDate = currentTime - 86400000L; // 1 day ago

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedPickupDateRange(new DateRange(fromDate, null)); // Only from date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle from-only date range");
        // Verify that all returned trips have expected pickup >= fromDate
        response.getData().forEach(trip -> {
            if (trip.getExpectedTimes() != null && trip.getExpectedTimes().getStartAt() != null) {
                assertTrue(trip.getExpectedTimes().getStartAt() >= fromDate,
                    "Expected pickup time should be >= from date");
            }
        });
    }

    @Test
    void listTrips_WithExpectedPickupDateToOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only to date (<= condition)
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long toDate = currentTime + 86400000L; // 1 day from now

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedPickupDateRange(new DateRange(null, toDate)); // Only to date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle to-only date range");
        // Verify that all returned trips have expected pickup <= toDate
        response.getData().forEach(trip -> {
            if (trip.getExpectedTimes() != null && trip.getExpectedTimes().getStartAt() != null) {
                assertTrue(trip.getExpectedTimes().getStartAt() <= toDate,
                    "Expected pickup time should be <= to date");
            }
        });
    }

    @Test
    void listTrips_WithExpectedDeliveryDateFromOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only from date for delivery
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long fromDate = currentTime - 86400000L; // 1 day ago

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedDeliveryDateRange(new DateRange(fromDate, null)); // Only from date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle delivery from-only date range");
        // Verify that all returned trips have expected delivery >= fromDate
        response.getData().forEach(trip -> {
            if (trip.getExpectedTimes() != null && trip.getExpectedTimes().getEndAt() != null) {
                assertTrue(trip.getExpectedTimes().getEndAt() >= fromDate,
                    "Expected delivery time should be >= from date");
            }
        });
    }

    @Test
    void listTrips_WithExpectedDeliveryDateToOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only to date for delivery
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long toDate = currentTime + 86400000L; // 1 day from now

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedDeliveryDateRange(new DateRange(null, toDate)); // Only to date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle delivery to-only date range");
        // Verify that all returned trips have expected delivery <= toDate
        response.getData().forEach(trip -> {
            if (trip.getExpectedTimes() != null && trip.getExpectedTimes().getEndAt() != null) {
                assertTrue(trip.getExpectedTimes().getEndAt() <= toDate,
                    "Expected delivery time should be <= to date");
            }
        });
    }

    @Test
    void listTrips_WithActualPickupDateFromOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only from date for actual pickup
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long fromDate = currentTime - 86400000L; // 1 day ago

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualPickupDateRange(new DateRange(fromDate, null)); // Only from date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle actual pickup from-only date range");
        // Verify that all returned trips have actual pickup >= fromDate
        response.getData().forEach(trip -> {
            if (trip.getActualTimes() != null && trip.getActualTimes().getStartAt() != null) {
                assertTrue(trip.getActualTimes().getStartAt() >= fromDate,
                    "Actual pickup time should be >= from date");
            }
        });
    }

    @Test
    void listTrips_WithActualPickupDateToOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only to date for actual pickup
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long toDate = currentTime + 86400000L; // 1 day from now

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualPickupDateRange(new DateRange(null, toDate)); // Only to date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle actual pickup to-only date range");
        // Verify that all returned trips have actual pickup <= toDate
        response.getData().forEach(trip -> {
            if (trip.getActualTimes() != null && trip.getActualTimes().getStartAt() != null) {
                assertTrue(trip.getActualTimes().getStartAt() <= toDate,
                    "Actual pickup time should be <= to date");
            }
        });
    }

    @Test
    void listTrips_WithActualDeliveryDateFromOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only from date for actual delivery
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long fromDate = currentTime - 86400000L; // 1 day ago

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualDeliveryDateRange(new DateRange(fromDate, null)); // Only from date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle actual delivery from-only date range");
        // Verify that all returned trips have actual delivery >= fromDate
        response.getData().forEach(trip -> {
            if (trip.getActualTimes() != null && trip.getActualTimes().getEndAt() != null) {
                assertTrue(trip.getActualTimes().getEndAt() >= fromDate,
                    "Actual delivery time should be >= from date");
            }
        });
    }

    @Test
    void listTrips_WithActualDeliveryDateToOnly_ShouldFilterCorrectly() throws Exception {
        // Arrange - Test with only to date for actual delivery
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long toDate = currentTime + 86400000L; // 1 day from now

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualDeliveryDateRange(new DateRange(null, toDate)); // Only to date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle actual delivery to-only date range");
        // Verify that all returned trips have actual delivery <= toDate
        response.getData().forEach(trip -> {
            if (trip.getActualTimes() != null && trip.getActualTimes().getEndAt() != null) {
                assertTrue(trip.getActualTimes().getEndAt() <= toDate,
                    "Actual delivery time should be <= to date");
            }
        });
    }

    @Test
    void listTrips_WithEmptyDateRange_ShouldReturnAllTrips() throws Exception {
        // Arrange - Test with empty date range (both from and to are null)
        TenantContext.setCurrentTenant("CFR");

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedPickupDateRange(new DateRange(null, null)); // Empty range
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(4, response.getTotalRecords(), "Empty date range should return all trips");
    }

    @Test
    void listTrips_WithMultipleDateRangeFilters_ShouldApplyAllFilters() throws Exception {
        // Arrange - Test with multiple date range filters applied together
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long fromDate = currentTime - 86400000L; // 1 day ago
        long toDate = currentTime + 86400000L; // 1 day from now

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedPickupDateRange(new DateRange(fromDate, null)); // From only
        filter.setExpectedDeliveryDateRange(new DateRange(null, toDate)); // To only
        filter.setActualPickupDateRange(new DateRange(fromDate, toDate)); // Both from and to
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle multiple date range filters");
        // All filters should be applied together (AND condition)
    }

    @Test
    void listTrips_WithFutureDateRangeOnly_ShouldReturnEmptyResult() throws Exception {
        // Arrange - Test with date range far in the future (should return no results)
        TenantContext.setCurrentTenant("CFR");
        long futureDate = System.currentTimeMillis() + (365L * 24 * 60 * 60 * 1000); // 1 year from now

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedPickupDateRange(new DateRange(futureDate, null)); // Far future from date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords(), "Future date range should return no trips");
        assertTrue(response.getData().isEmpty(), "Result list should be empty");
    }

    @Test
    void listTrips_WithPastDateRangeOnly_ShouldReturnEmptyResult() throws Exception {
        // Arrange - Test with date range far in the past (should return no results)
        TenantContext.setCurrentTenant("CFR");
        long pastDate = System.currentTimeMillis() - (365L * 24 * 60 * 60 * 1000); // 1 year ago

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedDeliveryDateRange(new DateRange(null, pastDate)); // Far past to date
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords(), "Past date range should return no trips");
        assertTrue(response.getData().isEmpty(), "Result list should be empty");
    }

    @Test
    void listTrips_WithVeryNarrowDateRange_ShouldFilterPrecisely() throws Exception {
        // Arrange - Test with a very narrow date range (1 second window)
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();
        long fromDate = currentTime - 1000L; // 1 second ago
        long toDate = currentTime + 1000L; // 1 second from now

        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualPickupDateRange(new DateRange(fromDate, toDate)); // Very narrow range
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0, "Should handle very narrow date ranges");
        // Verify precision of filtering
        response.getData().forEach(trip -> {
            if (trip.getActualTimes() != null && trip.getActualTimes().getStartAt() != null) {
                long actualPickup = trip.getActualTimes().getStartAt();
                assertTrue(actualPickup >= fromDate && actualPickup <= toDate,
                    "Actual pickup should be within the narrow date range");
            }
        });
    }

    @Test
    void listTrips_WithTransportOrderStatusesFilter_ShouldExecuteTransportOrderStatusSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTransportOrderStatuses(Arrays.asList("CONFIRMED", "COMPLETED"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithShipmentStatusesFilter_ShouldExecuteShipmentStatusSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("PLANNED", "CREATED"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithInvalidStatusesFilter_ShouldExecuteInvalidStatusSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripStatuses(Arrays.asList("INVALID_STATUS"));
        filter.setTransportOrderStatuses(Arrays.asList("INVALID_TO_STATUS"));
        filter.setShipmentStatuses(Arrays.asList("INVALID_SHIPMENT_STATUS"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Should return empty results for invalid statuses
        assertEquals(0, response.getTotalRecords());
    }

    @Test
    void listTrips_WithMixedValidInvalidStatusesFilter_ShouldExecuteMixedStatusSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripStatuses(Arrays.asList("ACTIVE", "INVALID_STATUS", "ENROUTE"));
        filter.setTransportOrderStatuses(Arrays.asList("CONFIRMED", "INVALID_TO_STATUS"));
        filter.setShipmentStatuses(Arrays.asList("PLANNED", "INVALID_SHIPMENT_STATUS"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Should filter out invalid statuses and process valid ones
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithCustomerOrderIdsFilter_ShouldExecuteCustomerOrderIdsSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setCustomerOrderIds(Arrays.asList("CO_COMPLEX_1", "CO_COMPLEX_2"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithVendorIdsFilter_ShouldExecuteVendorIdsSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setVendorIds(Arrays.asList("VENDOR_COMPLEX"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithConsignmentIdsFilter_ShouldExecuteConsignmentIdsSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setConsignmentIds(Arrays.asList("CONSIGNMENT_COMPLEX_1", "CONSIGNMENT_COMPLEX_2"));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithTransportOrderIdsFilter_ShouldExecuteTransportOrderIdsSpecification() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTransportOrderIds(Arrays.asList("TO_COMPLEX_" + uniqueId));
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithComplexCombinedFilters_ShouldExecuteMultipleSpecifications() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripStatuses(Arrays.asList("ACTIVE", "ENROUTE"));
        filter.setTransportOrderStatuses(Arrays.asList("CONFIRMED"));
        filter.setShipmentStatuses(Arrays.asList("PLANNED", "CREATED"));
        filter.setVehicleTypes(Arrays.asList("TRUCK"));
        filter.setOriginLocationId("ORIGIN_COMPLEX");

        long currentTime = System.currentTimeMillis();
        filter.setExpectedPickupDateRange(new DateRange(currentTime - 86400000L, currentTime + 86400000L));

        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithEmptyFilterCollections_ShouldExecuteEmptyCollectionSpecifications() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TripListingRequest.Filter filter = Faker.createEmptyCollectionsTripFilter();
        TripListingRequest request = Faker.createTripListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListTripsRequest(request);

        // Assert
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Empty collections should not filter anything, so should return all trips
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithDifferentSortingOptions_ShouldExecuteSortingLogic() throws Exception {
        // Test different sort fields and orders to cover sorting logic
        TenantContext.setCurrentTenant("CFR");

        // Test ASC sort
        TripListingRequest request = Faker.createTripListingRequestWithSort(new Sort("code", "ASC"));
        MvcResult result = performListTripsRequest(request);
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);

        // Test DESC sort
        request = Faker.createTripListingRequestWithSort(new Sort("createdAt", "DESC"));
        result = performListTripsRequest(request);
        response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);

        // Test status sort
        request = Faker.createTripListingRequestWithSort(new Sort("status", "ASC"));
        result = performListTripsRequest(request);
        response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listTrips_WithDifferentPaginationOptions_ShouldExecutePaginationLogic() throws Exception {
        // Test different pagination scenarios
        TenantContext.setCurrentTenant("CFR");

        // Test large page size
        TripListingRequest request = Faker.createTripListingRequestWithPagination(new Pagination(0, 100));
        MvcResult result = performListTripsRequest(request);
        ListResponse<TripListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);

        // Test second page
        request = Faker.createTripListingRequestWithPagination(new Pagination(1, 1));
        result = performListTripsRequest(request);
        response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    private MvcResult performListTripsRequest(TripListingRequest request) throws Exception {
        String requestBody = objectMapper.writeValueAsString(request);

        return mockMvc.perform(post("/v1/trips/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR")
                        .content(requestBody))
                .andExpect(status().isOk())
                .andReturn();
    }

    private ListResponse<TripListingResponse> parseResponse(MvcResult result) throws Exception {
        String responseJson = result.getResponse().getContentAsString();
        return objectMapper.readValue(responseJson, new TypeReference<ListResponse<TripListingResponse>>() {});
    }

    private void setupBasicTestData() {
        // Ensure tenant context is set for data operations
        TenantContext.setCurrentTenant("CFR");

        // Create basic Transport Order
        TransportOrder basicTransportOrder = new TransportOrder();
        basicTransportOrder.setCode("TO_BASIC_" + uniqueId);
        basicTransportOrder.setStatus(TransportOrderStatus.CONFIRMED);
        basicTransportOrder.setAssignmentType(AssignmentType.EXTERNAL);
        basicTransportOrder.setAssigneeIdentifier("VENDOR_BASIC");
        basicTransportOrder = transportOrderRepository.save(basicTransportOrder);

        // Create complex Transport Order for comprehensive testing
        TransportOrder complexTransportOrder = new TransportOrder();
        complexTransportOrder.setCode("TO_COMPLEX_" + uniqueId);
        complexTransportOrder.setStatus(TransportOrderStatus.CONFIRMED);
        complexTransportOrder.setAssignmentType(AssignmentType.EXTERNAL);
        complexTransportOrder.setAssigneeIdentifier("VENDOR_COMPLEX");
        complexTransportOrder = transportOrderRepository.save(complexTransportOrder);

        // Create comprehensive test trips
        List<Trip> trips = new ArrayList<>();

        // Basic Trip 1
        Trip trip1 = createBasicTrip("TRIP_BASIC_1_" + uniqueId, TripStatus.ACTIVE, basicTransportOrder,
                                   "ORIGIN_BASIC", "DEST_BASIC");
        trips.add(trip1);

        // Basic Trip 2
        Trip trip2 = createBasicTrip("TRIP_BASIC_2_" + uniqueId, TripStatus.ENROUTE, basicTransportOrder,
                                   "ORIGIN_BASIC_2", "DEST_BASIC_2");
        trips.add(trip2);

        // Complex Trip 1 - with comprehensive data for filter testing
        Trip complexTrip1 = createComplexTrip("TRIP_COMPLEX_1_" + uniqueId, TripStatus.ACTIVE, complexTransportOrder,
                                             "ORIGIN_COMPLEX", "DEST_COMPLEX", 1);
        trips.add(complexTrip1);

        // Complex Trip 2 - with comprehensive data for filter testing
        Trip complexTrip2 = createComplexTrip("TRIP_COMPLEX_2_" + uniqueId, TripStatus.ENROUTE, complexTransportOrder,
                                             "ORIGIN_COMPLEX_2", "DEST_COMPLEX_2", 2);
        trips.add(complexTrip2);

        tripRepository.saveAll(trips);
    }

    private Trip createBasicTrip(String code, TripStatus status, TransportOrder transportOrder,
                                String originCode, String destCode) {
        Trip trip = new Trip();
        trip.setCode(code);
        trip.setStatus(status);
        trip.setTransportOrder(transportOrder);
        trip.setExternalOriginLocationCode(originCode);
        trip.setExternalDestinationLocationCode(destCode);

        // Set timestamp fields for date range testing
        long currentTime = System.currentTimeMillis();
        trip.setExpectedPickupAt(currentTime);
        trip.setExpectedDeliveryAt(currentTime + 3600000L); // +1 hour
        trip.setActualPickupAt(currentTime + 1800000L); // +30 minutes
        trip.setActualDeliveryAt(currentTime + 5400000L); // +1.5 hours

        return trip;
    }

    private Trip createComplexTrip(String code, TripStatus status, TransportOrder transportOrder,
                                  String originCode, String destCode, int index) {
        Trip trip = createBasicTrip(code, status, transportOrder, originCode, destCode);

        // Add Vehicle Resource with proper data for filtering tests
        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId("VEHICLE_COMPLEX_" + index);
        vehicleResource.setExternalVehicleTypeId("TRUCK");
        vehicleResource.setRegistrationNumber("REG_COMPLEX_" + index);
        vehicleResource.setTrip(trip);
        trip.setVehicleResource(vehicleResource);

        // Add Trailer Resources
        Set<TrailerResource> trailerResources = new HashSet<>();
        TrailerResource trailerResource = new TrailerResource();
        trailerResource.setExternalResourceId("TRAILER_COMPLEX_" + index);
        trailerResource.setTrip(trip);
        trailerResources.add(trailerResource);
        trip.setTrailerResources(trailerResources);

        // Add Vehicle Operator Resources
        Set<VehicleOperatorResource> operatorResources = new HashSet<>();
        VehicleOperatorResource operatorResource = new VehicleOperatorResource();
        operatorResource.setExternalResourceId("DRIVER_COMPLEX_" + index);
        operatorResource.setTrip(trip);
        operatorResources.add(operatorResource);
        trip.setVehicleOperatorResources(operatorResources);

        // Add Shipments
        Set<Shipment> shipments = new HashSet<>();
        Shipment shipment = new Shipment();
        shipment.setCode("SHIPMENT_COMPLEX_" + index + "_" + uniqueId);
        shipment.setExternalCustomerOrderId("CO_COMPLEX_" + index);
        shipment.setExternalConsignmentId("CONSIGNMENT_COMPLEX_" + index);
        shipment.setStatus(ShipmentStatus.ALLOCATED);
        shipment.setTrip(trip);
        shipments.add(shipment);
        trip.setShipments(shipments);

        return trip;
    }
}
