trigger:
  - development
  - Release-v*

variables:
  - name: GRADLE_USER_HOME
    value: $(Pipeline.Workspace)/.gradle

resources:
  repositories:
    - repository: baseline_project_repo
      name: DevOps/_git/devops-common-utils
      type: git
      ref: main

pool:
  vmImage: ubuntu-latest
steps:

  - checkout: self   #my_current_repo
    path: s
    displayName: Checking out my_current_repo

  - checkout: baseline_project_repo
    displayName: Cheking baseline_project_repo

  - template: /pipelines/sonar/java_gradle_template.yaml@baseline_project_repo
    parameters:
      projectName: TMS
      organization: dpwhotfsonline
      productName: tms
      serviceConnection: SonarQube Connection
      javaVersion: '1.21'
      repoName: ctms-move
      coverageXmlPath: build/reports/jacoco/test/jacocoTestReport.xml
      exclusion: '**/*.bin,DevOps_Scripts/**,gradle/**,libs/**,**/entity/**,**/enums/**,**/constants/**,**/config/**,**/request/**,**/response/**,**/schemaobject/**,**/util/**,**/dataobject/**,**/kafka/consumer/**,**/kafka/response/**,**/dto/**,**/mapper/**'