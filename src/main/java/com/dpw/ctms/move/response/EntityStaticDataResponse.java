package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Contains the entity type identifier and its available status options.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EntityStaticDataResponse {

    private String entityType;

    private List<StatusOption> options;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StatusOption {
        
        private String label;

        private String value;
    }
}
