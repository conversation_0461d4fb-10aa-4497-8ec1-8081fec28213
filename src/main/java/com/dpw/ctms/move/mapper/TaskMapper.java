package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.TaskDTO;
import com.dpw.ctms.move.dto.TripTasksDetailsDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.request.TaskRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TaskParamMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TaskMapper {

    TaskDTO toDTO(TaskRequest taskRequest);

    Task toEntity(TaskDTO taskDTO);

    /**
     * Maps a single Task entity to TripTasksDetailsDTO
     * @param task - The Task entity to map
     * @return TripTasksDetailsDTO - The mapped DTO
     */
    @Mappings({
            @Mapping(target = "externalTaskRegistrationCode", source = "externalTaskRegistrationCode"),
            @Mapping(target = "sequence", expression = "java(task.getSequence() != null ? task.getSequence() : null)"),
            @Mapping(target = "status", source = "status"),
            @Mapping(target = "externalTaskMasterCode", source = "externalTaskMasterCode"),
            @Mapping(target = "expectedStartAt", source = "expectedStartAt"),
            @Mapping(target = "expectedEndAt", source = "expectedEndAt"),
            @Mapping(target = "actualStartAt", source = "actualStartAt"),
            @Mapping(target = "actualEndAt", source = "actualEndAt"),
            @Mapping(target = "updatedAt", source = "updatedAt")
    })
    TripTasksDetailsDTO toDTO(Task task);
}