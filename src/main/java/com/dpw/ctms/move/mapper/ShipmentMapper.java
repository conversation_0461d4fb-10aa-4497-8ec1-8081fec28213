package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.ShipmentDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ShipmentMapper {
    @Mapping(target = "originStop", ignore = true)
    @Mapping(target = "destinationStop", ignore = true)
    Shipment toEntity(ShipmentDTO dto);

    @Mapping(target = "tripCode", source = "trip.code")
    @Mapping(target = "customerOrderId", source = "externalCustomerOrderId")
    @Mapping(target = "consignmentId", source = "externalConsignmentId")
    @Mapping(target = "transportOrderCode", source = "transportOrder.code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatusInfo")
    @Mapping(target = "updatedAt", source = "updatedAt")
    @Mapping(target = "expectedTimes", source = ".", qualifiedByName = "mapShipmentExpectedTimes")
    @Mapping(target = "actualTimes", source = ".", qualifiedByName = "mapShipmentActualTimes")
    @Mapping(target = "originStop", source = "originStop", qualifiedByName = "mapStop")
    @Mapping(target = "destinationStop", source = "destinationStop", qualifiedByName = "mapStop")
    ShipmentListingResponse toResponse(Shipment shipment);

    @Named("mapStatusInfo")
    default EnumLabelValueResponse mapStatusInfo(Enum<?> status) {
        if (status == null) {
            return null;
        }

        if (status instanceof DisplayableStatusEnum displayableStatus) {
            return new EnumLabelValueResponse(displayableStatus.getDisplayName(), status.name());
        }

        return new EnumLabelValueResponse(status.name(), status.name());
    }

    @Named("mapShipmentExpectedTimes")
    default ShipmentListingResponse.TimeRange mapShipmentExpectedTimes(Shipment shipment) {
        return ShipmentListingResponse.TimeRange.builder()
                .startAt(shipment.getExpectedPickupAt())
                .endAt(shipment.getExpectedDeliveryAt())
                .build();
    }

    @Named("mapShipmentActualTimes")
    default ShipmentListingResponse.TimeRange mapShipmentActualTimes(Shipment shipment) {
        return ShipmentListingResponse.TimeRange.builder()
                .startAt(shipment.getActualPickupAt())
                .endAt(shipment.getActualDeliveryAt())
                .build();
    }

    @Named("mapStop")
    default ShipmentListingResponse.Stop mapStop(Stop stop) {
        if (stop == null) {
            return null;
        }

        return ShipmentListingResponse.Stop.builder()
                .id(stop.getId() != null ? stop.getId().toString() : null)
                .code(stop.getCode())
                .externalLocationCode(stop.getExternalLocationCode())
                .status(mapStatusInfo(stop.getStatus()))
                .updatedAt(stop.getUpdatedAt())
                .build();
    }
}
