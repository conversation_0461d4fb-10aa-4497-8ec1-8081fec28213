package com.dpw.ctms.move.integration;


import com.dpw.ctms.move.enums.ConfigType;
import com.dpw.ctms.move.service.IConfigService;
import com.dpw.tmsutils.exception.TMSException;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.CONFIG_SERVICE_NOT_FOUND;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INTERNAL_ERROR;

@Component
@RequiredArgsConstructor
@Slf4j
public class ConfigServiceIntegrator {
    private final List<IConfigService> configServices;
    private final Map<ConfigType, IConfigService> configServiceMap = new HashMap<>();

    /** This is responsible for registering any new config classes implementing IConfigService automatically
     * during application startup.**/
    @PostConstruct
    public void init(){
        for (IConfigService configService : configServices) {
            configServiceMap.put(configService.getConfigType(), configService);
        }
    }
    public <T extends IConfigService> T getConfigService(ConfigType configType, Class<T> classToBeCasted) {
        IConfigService configService = configServiceMap.get(configType);
        if (configService == null) {
            log.error("No config service found for type: {}", configType);
            throw new TMSException(INTERNAL_ERROR.name(),
                    String.format(CONFIG_SERVICE_NOT_FOUND, configType));
        }
        return classToBeCasted.cast(configService);
    }
}
