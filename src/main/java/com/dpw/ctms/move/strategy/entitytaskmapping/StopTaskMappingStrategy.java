package com.dpw.ctms.move.strategy.entitytaskmapping;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.dto.ParamValueStopDTO;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StopTaskEvent;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.util.EnumUtils;
import org.springframework.stereotype.Component;

@Component
public class StopTaskMappingStrategy implements IEntityTaskMappingStrategy{
    @Override
    public void mapEntityTask(ParamValueBaseDTO paramValueDTO, Trip trip, Task task) {
        ParamValueStopDTO stopDTO = (ParamValueStopDTO) paramValueDTO;
        for (Stop stop : trip.getStops()) {
            if (isTaskStopMappingPresent(stopDTO, stop)) {
                StopTask stopTask = createStopTask(stop, task, stopDTO);
                createStopTaskBackreferences(task, stop, stopTask);
            }
        }
    }

    @Override
    public TaskParamType getParamType() {
        return TaskParamType.STOP;
    }

    private StopTask createStopTask(Stop stop, Task task, ParamValueStopDTO paramValueStopDTO) {
        return StopTask.builder()
                .stop(stop)
                .task(task)
                .taskEvent(EnumUtils.getEnumFromString(StopTaskEvent.class,paramValueStopDTO.getTaskEvent(), false))
                .build();
    }

    private boolean isTaskStopMappingPresent(ParamValueStopDTO stopDTO, Stop stop) {
        return stopDTO.getExternalLocationCode().equals(stop.getExternalLocationCode())
                && stopDTO.getSequence().equals(stop.getSequence());
    }

    private void createStopTaskBackreferences(Task task, Stop stop, StopTask stopTask) {
        task.setStopTask(stopTask);
        stop.getStopTasks().add(stopTask);
    }
}
