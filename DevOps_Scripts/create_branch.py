import requests
import json
import os
import sys
from requests.auth import HTTPBasicAuth

#Variables
repo_name = sys.argv[1]
main_branch = sys.argv[2]
release_branch = sys.argv[3]
token = os.environ['AccessToken']
headers = {'Content-Type': 'application/json'}
branch_url = 'https://dev.azure.com/dpwhotfsonline/TMS/_apis/git/repositories/'+repo_name+'/refs?api-version=6.0'
repo_url = 'https://dev.azure.com/dpwhotfsonline/TMS/_apis/git/repositories/'+repo_name+'?api-version=6.0'
main_branch_Id = ''
repo_Id = ''

#Get Main Branch ID
get_main_branch = requests.get(branch_url, auth=HTTPBasicAuth('', token))
get_main_branch_json = get_main_branch.json()
if get_main_branch_json:
    for x in get_main_branch_json['value']:
        if x.get('name') == "refs/heads/"+main_branch+"":
           main_branch_Id = x.get('objectId')
           print("The Main Branch ID for "+main_branch+" is : "+main_branch_Id)

#Get Repo ID
get_repo = requests.get(repo_url, auth=HTTPBasicAuth('', token))
get_repo_json = get_repo.json()
if get_repo_json:
    repo_Id = get_repo_json['id']
    print("The Repo ID for "+repo_name+" is : "+repo_Id)
    print("##vso[task.setvariable variable=repo_Id]" + repo_Id)

create_branch_data = " [                                            \
    {                                                               \
    'name': 'refs/heads/"+release_branch+"',                        \
    'oldObjectId': '0000000000000000000000000000000000000000',      \
    'newObjectId': '"+main_branch_Id+"'                                  \
    }                                                               \
]"

#Create new Branch with Release version
response = requests.post(branch_url, data=create_branch_data, headers=headers, auth=HTTPBasicAuth('', token))
response_json = response.json()
print(response_json)
for x in response_json['value']:
    if x.get('success'):
        print("*****Request Succeeded*****")
    else:
        print("*****Request Failed*****")
        exit(1)