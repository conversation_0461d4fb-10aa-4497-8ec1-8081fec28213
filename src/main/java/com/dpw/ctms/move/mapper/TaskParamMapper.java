package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.dto.TaskParamDTO;
import com.dpw.ctms.move.request.TaskParamRequest;
import com.dpw.ctms.move.resolver.TaskParamValueResolver;
import lombok.RequiredArgsConstructor;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@RequiredArgsConstructor
public abstract class TaskParamMapper {
    @Autowired
    protected TaskParamValueResolver resolver;

    @Mapping(target = "paramValue", ignore = true)
    public abstract TaskParamDTO toDto(TaskParamRequest request);

    @AfterMapping
    protected void handleParamValue(TaskParamRequest request, @MappingTarget TaskParamDTO dto) {
        ParamValueBaseDTO paramValue = resolveParamValue(dto, request);
        dto.setParamValue(paramValue);
    }

    private ParamValueBaseDTO resolveParamValue(TaskParamDTO dto, TaskParamRequest request) {
        return resolver.resolve(dto.getParamName(), request.getParamValue());
    }
}
