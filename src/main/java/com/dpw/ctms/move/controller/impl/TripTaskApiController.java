package com.dpw.ctms.move.controller.impl;


import com.dpw.ctms.move.controller.TripTaskApi;
import com.dpw.ctms.move.request.FilterRequest;
import com.dpw.ctms.move.request.TripTaskListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripFacilityTasksDetailsResponse;
import com.dpw.ctms.move.service.TripStopTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class TripTaskApiController implements TripTaskApi {
    private final TripStopTaskService tripStopTaskService;
    @Override
    public ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> getTripTasksByFacility(
            String tripCode, FilterRequest<TripTaskListingRequest> tripTaskListingRequest) {
        return new ResponseEntity<>(tripStopTaskService.getTripTasks(tripCode,tripTaskListingRequest), HttpStatus.OK);
    }

}
