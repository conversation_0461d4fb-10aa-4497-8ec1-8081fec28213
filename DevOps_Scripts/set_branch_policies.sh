#!/bin/bash

#Variables
org=https://dpwhotfsonline.visualstudio.com/
project=TMS
#repo_Id='145fcf5c-d838-4cf7-9d1a-1de1d1926406'
repo_Id=$1
#branch_name='Release-v1.2.1'
branch_name=$2
approver_count=2
reviewer_ID_QA="4780e000-07a9-4776-947e-36b29f71040d"
reviewer_ID_Dev="628a6f3d-939a-4019-af62-119b28398a6d"
az_params="--blocking true --enabled true --branch $branch_name --repository-id $repo_Id --branch-match-type exact --org $org --project $project"
count=0

#Authentication
echo $AccessToken | az devops login

#Minimum number of reviewers
az repos policy approver-count create --allow-downvotes false \
                                      --creator-vote-counts false \
                                      --minimum-approver-count $approver_count \
                                      --reset-on-source-push false $az_params
if [ `echo $?` == 0 ]; then
    echo "Success adding Min no of Reviewers"
else
    echo "Failure adding Min no of Reviewers"
    count=$(($count+1))
fi

#Check for comment resolution
az repos policy comment-required create $az_params
if [ `echo $?` == 0 ]; then
    echo "Success adding comment resolution"
else
    echo "Failure adding comment resolution"
    count=$(($count+1))
fi

#Check for linked workitems
az repos policy work-item-linking create $az_params
if [ `echo $?` == 0 ]; then
    echo "Success adding work-item-linking"
else
    echo "Failure adding work-item-linking"
    count=$(($count+1))
fi

#Merge type
az repos policy merge-strategy create --allow-no-fast-forward true $az_params
if [ `echo $?` == 0 ]; then
    echo "Success adding merge-strategy"
else
    echo "Failure adding merge-strategy"
    count=$(($count+1))
fi

az repos policy build create --build-definition-id 3276 \
                            --display-name "Sonar Build" \
                            --enabled true \
                            --manual-queue-only false \
                            --queue-on-source-update-only true \
                            --valid-duration 720 $az_params

if [ `echo $?` == 0 ]; then
   echo "Successfully added sonar pipeline"
else
   echo "Failure adding sonar pipeline"
   count=$(($count+1))
fi

# Dev Approval Group
az repos policy required-reviewer create --message "Need to get approval from Dev team" \
                                         --required-reviewer-ids $reviewer_ID_Dev \
                                         --path-filter '/*' $az_params
                                       
if [ `echo $?` == 0 ]; then
    echo "Success adding required-reviewer - Dev Team"
else
    echo "Failure adding required-reviewer - Dev Team"
    count=$(($count+1))
fi

# QA Approval Group
az repos policy required-reviewer create --message "Need to get approval from QA team" \
                                         --required-reviewer-ids $reviewer_ID_QA \
                                         --path-filter '/*' $az_params
                                       
if [ `echo $?` == 0 ]; then
    echo "Success adding required-reviewer - QA Team"
else
    echo "Failure adding required-reviewer - QA Team"
    count=$(($count+1))
fi

if [ $count -gt 0 ]; then
    echo "Setting all branch policies failed. Please check above logs"
    exit 1
else
    echo "Setting all branch policies succeeded"
fi