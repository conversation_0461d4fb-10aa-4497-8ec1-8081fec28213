package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.StopDTO;
import com.dpw.ctms.move.request.StopRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import java.util.UUID;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface StopMapper {
    @Mapping(target = "code", expression = "java(generateStopCode())")
    StopDTO toDTO(StopRequest stopRequest);

    /** TODO handle code generation in a separate util **/
    default String generateStopCode() {
        return UUID.randomUUID().toString();
    }
}
