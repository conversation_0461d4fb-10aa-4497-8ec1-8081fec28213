package com.dpw.ctms.move.entity;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

/**TODO AuditListener to be implemented **/
@Setter
@Getter
@MappedSuperclass
@Audited
public class BaseEntity {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "created_at", nullable = false, updatable = false)
    Long createdAt = System.currentTimeMillis();

    @Column(name = "updated_at")
    Long updatedAt;

    @Column(name = "created_by", nullable = false, updatable = false)
    String createdBy = "SYSTEM";

    @Column(name = "updated_by")
    String updatedBy;
}
