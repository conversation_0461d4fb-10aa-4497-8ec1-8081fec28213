package com.dpw.ctms.move.statemachine;

import com.dpw.ctms.move.dto.StateMachineTenantDTO;
import com.dpw.ctms.move.util.FileReaderUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class StateMachineConfigReader {

    private static final String CONFIG_FILE = "state_machine_config.json";
    private static final String PATH = "config/";

    public Map<String, StateMachineTenantDTO> readStateMachineConfig() {
        return FileReaderUtil.jsonFileReader(PATH, CONFIG_FILE, new TypeReference<>() {});
    }
}