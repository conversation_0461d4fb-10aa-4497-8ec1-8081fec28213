package com.dpw.ctms.move.statemachine.config;

import com.dpw.ctms.move.statemachine.guard.MandatoryTaskCompletionGuard;
import com.dpw.ctms.move.statemachine.registry.TaskGuardRegistry;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.guard.Guard;

import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class TaskGuardRegistryConfig {

    private final MandatoryTaskCompletionGuard mandatoryTaskCompletionGuard;

    @Bean
    public TaskGuardRegistry taskGuardRegistry() {
        Map<String, Guard<String, String>> guardMap = new HashMap<>();
        guardMap.put("TASK_TO_CLOSED_GUARD_ID", mandatoryTaskCompletionGuard);
        return new TaskGuardRegistry(guardMap);
    }
}

