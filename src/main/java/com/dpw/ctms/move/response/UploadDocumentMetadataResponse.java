package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadDocumentMetadataResponse {
    private String client;
    private String fileName;
    private Integer remainingWriteOperations;
    private String clientIdentifier;
    private String fileIdentifier;
    private String fileKey;
    private String writeExpiryTimeStamp;
    private String presignedUploadUrl;
    private String presignedDownloadUrl;
    private Integer fileSize;
    private String fileType;
    private String subscriptionName;
}
