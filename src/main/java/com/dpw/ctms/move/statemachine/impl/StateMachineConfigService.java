package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.dto.StateMachineTenantDTO;
import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.enums.ConfigType;
import com.dpw.ctms.move.service.IConfigService;
import com.dpw.ctms.move.statemachine.StateMachineConfigReader;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.STATE_MACHINE_CONFIG_NOT_FOUND;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Component
@RequiredArgsConstructor
@Slf4j
public class StateMachineConfigService implements IConfigService {

    private final StateMachineConfigReader stateMachineConfigReader;

    @Override
    public ConfigType getConfigType() {
        return ConfigType.STATE_MACHINE;
    }

    public StateTransitionHolderDTO getTaskStateMachineConfig(String tenantId) {
        return getTenantConfig(tenantId).getTask();
    }

    private StateMachineTenantDTO getTenantConfig(String tenantId) {
        Map<String, StateMachineTenantDTO> tenantConfigs = stateMachineConfigReader.readStateMachineConfig();
        StateMachineTenantDTO tenantDTO = tenantConfigs.get(tenantId);
        if (tenantDTO == null) {
            log.error("No state machine config found for tenantId: {} ", tenantId);
            throw new TMSException(INVALID_REQUEST.name(), String.format(STATE_MACHINE_CONFIG_NOT_FOUND, tenantId));
        }
        log.info("State machine tenant configs {}", tenantDTO);
        return tenantDTO;
    }
}
