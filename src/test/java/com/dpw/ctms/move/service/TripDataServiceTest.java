package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.repository.StopRepository;
import com.dpw.ctms.move.repository.StopTaskRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.service.impl.TripDataServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.*;

import static com.dpw.ctms.move.enums.StopStatus.DISCARDED;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TripDataServiceTest {

    @Mock
    private StopTaskRepository stopTaskRepository;

    @Mock
    private StopRepository stopRepository;

    @Mock
    private TripRepository tripRepository;

    @InjectMocks
    private TripDataServiceImpl tripDataService;

    private Trip mockTrip;
    private Stop mockStop1;
    private Stop mockStop2;
    private Pagination mockPagination;

    @BeforeEach
    void setUp() {
        mockTrip = new Trip();
        mockTrip.setCode("TRIP001");
        mockTrip.setId(1L);

        mockStop1 = new Stop();
        mockStop1.setId(1L);
        mockStop1.setTrip(mockTrip);

        mockStop2 = new Stop();
        mockStop2.setId(2L);
        mockStop2.setTrip(mockTrip);

        mockPagination = new Pagination();
        mockPagination.setPageNo(0);
        mockPagination.setPageSize(10);
    }

    @Test
    void getTripByCode_WhenTripExists_ShouldReturnTrip() {
        // Given
        String tripCode = "TRIP001";
        when(tripRepository.findByCode(tripCode)).thenReturn(Optional.of(mockTrip));

        // When
        Trip result = tripDataService.getTripByCode(tripCode);

        // Then
        assertNotNull(result);
        assertEquals(tripCode, result.getCode());
        verify(tripRepository, times(1)).findByCode(tripCode);
    }

    @Test
    void getTripByCode_WhenTripDoesNotExist_ShouldThrowTMSException() {
        // Given
        String tripCode = "NONEXISTENT";
        when(tripRepository.findByCode(tripCode)).thenReturn(Optional.empty());

        // When & Then
        TMSException exception = assertThrows(TMSException.class,
                () -> tripDataService.getTripByCode(tripCode));

        assertEquals("INVALID_REQUEST", exception.getErrorCode());
        assertEquals("Trip not found with code: " + tripCode, exception.getErrorMessage());
        verify(tripRepository, times(1)).findByCode(tripCode);
    }

    @Test
    void getTripByCode_WithNullTripCode_ShouldCallRepositoryWithNull() {
        // Given
        String tripCode = null;
        when(tripRepository.findByCode(null)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(TMSException.class, () -> tripDataService.getTripByCode(tripCode));
        verify(tripRepository, times(1)).findByCode(null);
    }

    @Test
    void getTripStopByTripCode_WhenStopsExist_ShouldReturnStopList() {
        // Given
        String tripCode = "TRIP001";
        List<Stop> expectedStops = Arrays.asList(mockStop1, mockStop2);
        PageRequest expectedPageRequest = PageRequest.of(0, 10);
        Page<Stop> expectedStopsPage = new PageImpl<>(expectedStops);
        when(stopRepository.findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                eq(tripCode),
                eq(List.of(DISCARDED.name())),
                eq(tripCode),
                eq(expectedPageRequest)
        )).thenReturn(expectedStopsPage);

        // When
        List<Stop> result = tripDataService.getTripStopByTripCode(tripCode, mockPagination).getContent();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedStops, result);
        verify(stopRepository, times(1)).findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                tripCode, List.of(DISCARDED.name()), tripCode, expectedPageRequest);
    }

    @Test
    void getTripStopByTripCode_WithDifferentPagination_ShouldUseCorrectPageRequest() {
        // Given
        String tripCode = "TRIP001";
        Pagination customPagination = new Pagination();
        customPagination.setPageNo(2);
        customPagination.setPageSize(5);
        PageRequest expectedPageRequest = PageRequest.of(2, 5);
        Page<Stop> expectedStopsPage = new PageImpl<>(Arrays.asList(mockStop1));
        when(stopRepository.findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                eq(tripCode),
                eq(List.of(DISCARDED.name())),
                eq(tripCode),
                eq(expectedPageRequest)
        )).thenReturn(expectedStopsPage);

        // When
        Page<Stop> result = tripDataService.getTripStopByTripCode(tripCode, customPagination);

        // Then
        assertNotNull(result);
        verify(stopRepository, times(1)).findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                tripCode, List.of(DISCARDED.name()), tripCode, expectedPageRequest);
    }

    @Test
    void getTripStopByTripCode_WhenNoStopsExist_ShouldReturnEmptyList() {
        // Given
        String tripCode = "TRIP001";
        Page<Stop> emptyPage = new PageImpl<>(Collections.emptyList());
        when(stopRepository.findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                anyString(), anyList(), anyString(), any(PageRequest.class)
        )).thenReturn(emptyPage);

        // When
        Page<Stop> result = tripDataService.getTripStopByTripCode(tripCode, mockPagination);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(stopRepository, times(1)).findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                anyString(), anyList(), anyString(), any(PageRequest.class));
    }

    @Test
    void getTripStopByTripCode_WithNullTripCode_ShouldPassNullToRepository() {
        // Given
        String tripCode = null;
        Page<Stop> emptyPage = new PageImpl<>(Collections.emptyList());
        when(stopRepository.findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                isNull(), anyList(), isNull(), any(PageRequest.class)
        )).thenReturn(emptyPage);

        // When
        Page<Stop> result = tripDataService.getTripStopByTripCode(tripCode, mockPagination);

        // Then
        assertNotNull(result);
        verify(stopRepository, times(1)).findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                isNull(), eq(List.of(DISCARDED.name())), isNull(), any(PageRequest.class));
    }

    @Test
    void getTotalStopCount_WhenStopsExist_ShouldReturnCount() {
        // Given
        String tripCode = "TRIP001";
        Long expectedCount = 5L;
        when(stopRepository.countDistinctByTrip_CodeAndStatusNotIn(
                tripCode, List.of(DISCARDED.name())
        )).thenReturn(expectedCount);

        // When
        Long result = tripDataService.getTotalStopCount(tripCode);

        // Then
        assertEquals(expectedCount, result);
        verify(stopRepository, times(1)).countDistinctByTrip_CodeAndStatusNotIn(
                tripCode, List.of(DISCARDED.name()));
    }

    @Test
    void getTotalStopCount_WhenNoStopsExist_ShouldReturnZero() {
        // Given
        String tripCode = "TRIP001";
        when(stopRepository.countDistinctByTrip_CodeAndStatusNotIn(
                tripCode, List.of(DISCARDED.name())
        )).thenReturn(0L);

        // When
        Long result = tripDataService.getTotalStopCount(tripCode);

        // Then
        assertEquals(0, result);
        verify(stopRepository, times(1)).countDistinctByTrip_CodeAndStatusNotIn(
                tripCode, List.of(DISCARDED.name()));
    }

    @Test
    void getTotalStopCount_WithNullTripCode_ShouldPassNullToRepository() {
        // Given
        String tripCode = null;
        when(stopRepository.countDistinctByTrip_CodeAndStatusNotIn(
                null, List.of(DISCARDED.name())
        )).thenReturn(0L);

        // When
        Long result = tripDataService.getTotalStopCount(tripCode);

        // Then
        assertEquals(0, result);
        verify(stopRepository, times(1)).countDistinctByTrip_CodeAndStatusNotIn(
                null, List.of(DISCARDED.name()));
    }


    @Test
    void getTotalStopCount_WhenRepositoryThrowsException_ShouldPropagateException() {
        // Given
        String tripCode = "TRIP001";
        RuntimeException expectedException = new RuntimeException("Database connection failed");
        when(stopRepository.countDistinctByTrip_CodeAndStatusNotIn(
                anyString(), anyList()
        )).thenThrow(expectedException);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> tripDataService.getTotalStopCount(tripCode));

        assertEquals("Database connection failed", exception.getMessage());
        verify(stopRepository, times(1)).countDistinctByTrip_CodeAndStatusNotIn(
                tripCode, List.of(DISCARDED.name()));
    }

    @Test
    void allMethods_ShouldUseDiscardedStatusCorrectly() {
        // This test verifies that all methods consistently use DISCARDED.name()
        String tripCode = "TRIP001";
        List<String> expectedDiscardedList = List.of(DISCARDED.name());

        Page<Stop> expectedStopsPage = new PageImpl<>(Arrays.asList(mockStop1));
        // Setup mocks
        when(tripRepository.findByCode(anyString())).thenReturn(Optional.of(mockTrip));
        when(stopRepository.findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                anyString(), anyList(), anyString(), any(PageRequest.class)
        )).thenReturn(expectedStopsPage);
        when(stopRepository.countDistinctByTrip_CodeAndStatusNotIn(
                anyString(), anyList()
        )).thenReturn(5L);

        // Execute all methods
        tripDataService.getTripByCode(tripCode);
        tripDataService.getTripStopByTripCode(tripCode, mockPagination);
        tripDataService.getTotalStopCount(tripCode);

        // Verify DISCARDED status is used consistently
        verify(stopRepository).findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(
                eq(tripCode), eq(expectedDiscardedList), eq(tripCode), any(PageRequest.class));
        verify(stopRepository).countDistinctByTrip_CodeAndStatusNotIn(
                tripCode, expectedDiscardedList);
    }
}