package com.dpw.ctms.move.util;

import com.dpw.tmsutils.exception.TMSException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for Enum operations.
 * Provides methods to safely convert strings to enum values.
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EnumUtils {
    /**
     * Converts a string to an enum value of the specified enum class.
     *
     * @param <T>         The enum type
     * @param enumClass   The class of the enum
     * @param stringValue The string value to convert
     * @param shouldThrow If true, throws an exception when conversion fails; if false, returns null
     * @return The corresponding enum value or null if conversion fails and shouldThrow is false
     * @throws TMSException If conversion fails and shouldThrow is true with error code "INVALID_ENUM_VALUE"
     */
    public static <T extends Enum<T>> T getEnumFromString(Class<T> enumClass, String stringValue, boolean shouldThrow) {
        if (enumClass == null || stringValue == null) {
            return null;
        }

        try {
            return Enum.valueOf(enumClass, stringValue.toUpperCase());
        } catch (IllegalArgumentException exception) {
            if (shouldThrow) {
                String errorMessage = String.format("Invalid enum value '%s' for enum type %s. Please provide a valid value.",
                        stringValue, enumClass.getCanonicalName());
                log.error(errorMessage);
                throw new TMSException("INVALID_ENUM_VALUE", errorMessage);
            } else {
                return null;
            }
        }
    }
}