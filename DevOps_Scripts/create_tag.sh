#! /bin/bash

AccessToken=$1
commit_id=$2
buildID=$3
repo_name=$4

echo $AccessToken | az devops login
echo "*****Check TAG on latest commit*****"
build_tag_check=`git rev-list -n 1 "${buildID}" 2>/dev/null | grep -w "${commit_id}"`
if [ ! -z $build_tag_check ]; then
    echo "*****Tag already present on the latest commit*****"
else
    echo "*****Tag not present. Creating it...*****"
    tag_status=`az repos ref create --name tags/${buildID} --object-id ${commit_id} --org https://dpwhotfsonline.visualstudio.com/ --project TMS --repository ${repo_name} | grep -w 'updateStatus' | cut -d'"' -f4`
    if [ "$tag_status" = "succeeded" ]; then
        echo "*****Tag creation successful on Mainline branch*****"
    else
        echo "*****Tag creation failed on Mainline branch*****"
        exit 1
    fi
fi