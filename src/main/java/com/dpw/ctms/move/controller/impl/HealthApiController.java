package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.HealthApi;
import com.dpw.ctms.move.service.IHealthService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class HealthApiController implements HealthApi {
    private final IHealthService healthService;

    @Override
    public String getHealthStatus() {
        return healthService.getHealthStatus();
    }
}
