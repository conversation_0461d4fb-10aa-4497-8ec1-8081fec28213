package com.dpw.ctms.move.strategy.taskparam;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.enums.TaskParamType;
import org.springframework.stereotype.Component;

@Component
public class TaskVehicleOperatorParamValueStrategy implements ITaskParamValueStrategy{
    @Override
    public TaskParamType getParamType() {
        return TaskParamType.VEHICLE_OPERATOR;
    }

    @Override
    public Class<? extends ParamValueBaseDTO>  getParamValue() {
        return ParamValueVehicleOperatorDTO.class;
    }
}
