package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.service.ITaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TaskOperationInDB {

    private final ITaskService taskService;

    public Task createTask() {
        return taskService.saveTask(Fakers.createTask());
    }

    public Task getTaskById(Long taskId) {
        return taskService.findTaskById(taskId);
    }
}
