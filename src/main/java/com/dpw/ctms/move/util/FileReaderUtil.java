package com.dpw.ctms.move.util;

import com.dpw.tmsutils.exception.TMSException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.ERROR_READING_JSON_FILE;

@Slf4j
public class FileReaderUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T jsonFileReader(String path, String fileName, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(
                    new ClassPathResource(path + fileName).getInputStream(),
                    typeReference
            );
        }
        catch (Exception e) {
            log.error("Error reading JSON file: {}. Exception: {}", fileName, e.getMessage());
            throw new TMSException(ERROR_READING_JSON_FILE, e.getMessage());
        }
    }
}
