package com.dpw.ctms.move.kafka.consumer;

import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TaskActionHandlerRegistry {

    private final Map<String, TaskActionHandler> handlerMap;
    private static final String PICKUP_TASK = "PICKUP_TASK";

    public TaskActionHandlerRegistry(
            PickupTaskHandler pickupTaskHandler) {
        this.handlerMap = Map.of(PICKUP_TASK, pickupTaskHandler);
    }

    public TaskActionHandler getHandler(String action) {
        return handlerMap.get(action);
    }
}
