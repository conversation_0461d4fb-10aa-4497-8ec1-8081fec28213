package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.tmsutils.annotation.ApiLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/v1/shipments")
public interface ShipmentApi {

    @PostMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Shipment listing")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully listed the shipments")
    })
    @ApiLog
    ResponseEntity<ListResponse<ShipmentListingResponse>> listShipments(
            @Valid
            @RequestBody
            @Parameter(name = "ShipmentListingRequest", required = true, description = "Request to list shipments")
            ShipmentListingRequest shipmentListingRequest);
}
