package com.dpw.ctms.move.fakers;

import com.dpw.ctms.move.dto.StateConfigDTO;
import com.dpw.ctms.move.dto.StateMachineTenantDTO;
import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.dto.TransitionConfigDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.github.javafaker.Faker;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Fakers {

    public static final Faker faker = new Faker();

    public static Map<String, StateMachineTenantDTO> createStateMachineTenantConfigMap(String tenantId) {
        StateMachineTenantDTO stateMachineTenantDTO = createStateMachineTenantDTO();
        Map<String, StateMachineTenantDTO> map = new HashMap<>();
        map.put(tenantId, stateMachineTenantDTO);
        return map;
    }

    public static Map<String, StateMachineTenantDTO> createStateMachineTenantConfigMapWithoutInitialState(String tenantId) {
        StateMachineTenantDTO stateMachineTenantDTO = createStateMachineTenantDTO();
        for (StateConfigDTO stateConfigDTO : stateMachineTenantDTO.getTask().getStates()) {
            stateConfigDTO.setIsInitial(false);
        }
        Map<String, StateMachineTenantDTO> map = new HashMap<>();
        map.put(tenantId, stateMachineTenantDTO);
        return map;
    }

    private static StateMachineTenantDTO createStateMachineTenantDTO() {
        return StateMachineTenantDTO.builder()
                .task(createStateTransitionHolder(StateMachineEntityType.TASK)).build();
    }

    private static StateTransitionHolderDTO createStateTransitionHolder(StateMachineEntityType stateMachineEntityType) {
        return switch (stateMachineEntityType) {
            case TASK ->
                    StateTransitionHolderDTO.builder().states(createStateConfig(stateMachineEntityType))
                            .transitions(createTransitionConfig(stateMachineEntityType)).build();
        };
    }

    private static List<StateConfigDTO> createStateConfig(StateMachineEntityType stateMachineEntityType) {
        return switch (stateMachineEntityType) {
            case TASK -> List.of(
                    StateConfigDTO.builder().state("CREATED").isInitial(true).build(),
                    StateConfigDTO.builder().state("COMPLETED").isInitial(false).build(),
                    StateConfigDTO.builder().state("CLOSED").isInitial(false).build()
            );
        };
    }

    private static List<TransitionConfigDTO> createTransitionConfig(StateMachineEntityType stateMachineEntityType) {
        return switch (stateMachineEntityType) {
            case TASK -> List.of(
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("COMPLETED")
                            .event("MARK_TASK_COMPLETED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("COMPLETED").targetState("CLOSED")
                            .event("MANDATORY_ATTRIBUTES_FILLED")
                            .guardId("TASK_TO_CLOSED_GUARD_ID")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("CLOSED")
                            .event("COMPLETED_TASK_WITH_ATTRIBUTES")
                            .guardId("TASK_TO_CLOSED_GUARD_ID")
                            .actionId("").build()
            );
        };
    }

    public static Task createTask() {
        return Task.builder().code(faker.lorem().sentence())
                .externalTaskMasterCode(faker.lorem().sentence())
                .sequence(1)
                .expectedStartAt(System.currentTimeMillis())
                .expectedEndAt(System.currentTimeMillis())
                .status(TaskStatus.CREATED).build();
    }
}
