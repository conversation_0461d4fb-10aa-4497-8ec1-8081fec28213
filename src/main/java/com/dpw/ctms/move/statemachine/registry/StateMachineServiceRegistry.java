package com.dpw.ctms.move.statemachine.registry;

import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.impl.TaskStateMachineServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StateMachineServiceRegistry {

    private final TaskStateMachineServiceImpl taskStateMachineService;

    public IStateMachineService<?> getService(StateMachineEntityType entityType) {
        return switch (entityType) {
            case TASK -> taskStateMachineService;
        };
    }
}
