package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.mapper.TripViewMapper;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.response.TripViewResponse;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.service.TripFilteringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Slf4j
@Service
@RequiredArgsConstructor
public class TripServiceImpl implements ITripService {

    private final TripFilteringService tripFilteringService;
    private final ITripDataService tripDataService;
    private final TripViewMapper tripViewMapper;

    /**
     * Lists trips based on the provided trip listing request.
     *
     * @param tripListingRequest the request containing pagination, sorting, and filtering criteria
     * @return a ListResponse containing the list of TripListingResponse objects and total records
     */
    @Override
    public ListResponse<TripListingResponse> listTrips(TripListingRequest tripListingRequest) {
        return tripFilteringService.filterTrips(tripListingRequest);
    }

    @Override
    @Transactional
    public TripViewResponse getTripView(String tripCode) {
        Trip trip = tripDataService.getTripByCodeWithAllDetails(tripCode);
        return tripViewMapper.toResponse(trip);
    }

}
