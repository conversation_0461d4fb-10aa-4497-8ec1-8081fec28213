package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.AssignmentStatus;
import com.dpw.ctms.move.enums.ResourceAssignmentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceAssignmentDetailsDTO {
    private AssignmentStatus assignmentStatus;
    private ResourceAssignmentType resourceAssignmentType;
    private Long assignedAt;
    private String assignedBy;

}
