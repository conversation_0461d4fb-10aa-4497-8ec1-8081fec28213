package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.enums.TaskStatus;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "stop")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Stop extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "external_location_code")
    private String externalLocationCode;

    @Column(name = "sequence")
    private Integer sequence;

    @Enumerated(EnumType.STRING)
    private StopStatus status;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "segment_details", columnDefinition = "jsonb")
    private SegmentDetails segmentDetails;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trip_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private Trip trip;

    @OneToMany(mappedBy = "stop", cascade = CascadeType.ALL)
    @Builder.Default
    private List<StopTask> stopTasks = new ArrayList<>();

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;

    @OneToMany(mappedBy = "originStop")
    @Builder.Default
    private List<Shipment> departureShipments = new ArrayList<>();

    @OneToMany(mappedBy = "destinationStop")
    @Builder.Default
    private List<Shipment> arrivalShipments = new ArrayList<>();

    public List<StopTask> getTripStopActiveTasks() {
        if (null == this.getStopTasks()) {
            return new ArrayList<>();
        }
        return this.getStopTasks().stream().filter(
                tripStopTask -> TaskStatus.DISCARDED != tripStopTask.getTask().getStatus())
                .collect(Collectors.toList());
    }
}
