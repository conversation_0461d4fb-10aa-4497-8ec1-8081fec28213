package com.dpw.ctms.move.statemachine.registry;

import lombok.RequiredArgsConstructor;
import org.springframework.statemachine.guard.Guard;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class TaskGuardRegistry implements IGuardRegistry {
    private final Map<String, Guard<String, String>> taskGuards;

    @Override
    public Guard<String, String> getGuard(String guardId) {
        return taskGuards.get(guardId);
    }
}
