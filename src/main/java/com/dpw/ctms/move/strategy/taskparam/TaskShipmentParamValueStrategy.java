package com.dpw.ctms.move.strategy.taskparam;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.enums.TaskParamType;
import org.springframework.stereotype.Component;

@Component
public class TaskShipmentParamValueStrategy implements ITaskParamValueStrategy{
    @Override
    public TaskParamType getParamType() {
        return TaskParamType.SHIPMENT;
    }

    @Override
    public Class<? extends ParamValueBaseDTO>  getParamValue() {
        return ParamValueShipmentDTO.class;
    }
}
