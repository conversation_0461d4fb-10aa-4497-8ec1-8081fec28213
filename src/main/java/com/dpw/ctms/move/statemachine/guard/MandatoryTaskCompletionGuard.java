package com.dpw.ctms.move.statemachine.guard;

import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;
import org.springframework.stereotype.Service;

/** Logic to make sure that we move to closed state until all the task are completed **/
@Service
@Slf4j
public class MandatoryTaskCompletionGuard implements Guard<String, String> {
    @Override
    public boolean evaluate(StateContext<String, String> context) {
        log.info("Mandatory task completion guard evaluation");
        boolean result = true;
        log.info("Mandatory task completion guard evaluation result: {}", result);
        return result;
    }
}