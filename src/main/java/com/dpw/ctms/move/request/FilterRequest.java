package com.dpw.ctms.move.request;

import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FilterRequest<T> {
    @Schema(description = "Filter criteria.")
    private T filters;

    @Schema(description = "Pagination details.")
    private Pagination pagination;

    @Schema(description = "Sorting options.")
    private Sort sortOption;
}