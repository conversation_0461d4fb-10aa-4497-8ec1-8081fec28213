package com.dpw.ctms.move.enums;

public enum TransportOrderStatus implements DisplayableStatusEnum {

    CONFIRMED("Confirmed"),
    REJECTED("Rejected"),
    CANCELLED("Cancelled"),
    COMPLETED("Completed");

    private String displayText;

    TransportOrderStatus(String displayText) {
        this.displayText = displayText;
    }

    @Override
    public String getDisplayName() {
        return this.displayText;
    }

    public static TransportOrderStatus fromDisplayName(String displayText) {
        if (displayText == null) {
            return null;
        }
        for (TransportOrderStatus status : TransportOrderStatus.values()) {
            if (status.displayText.equalsIgnoreCase(displayText)) {
                return status;
            }
        }
        return null;
    }
}
