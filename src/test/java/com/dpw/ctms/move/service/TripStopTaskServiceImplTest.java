package com.dpw.ctms.move.service;

import com.dpw.ctms.move.dto.LocationDetail;
import com.dpw.ctms.move.dto.TripTasksDetailsDTO;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.mapper.TaskMapper;
import com.dpw.ctms.move.request.FilterRequest;
import com.dpw.ctms.move.request.TripTaskListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripFacilityTasksDetailsResponse;
import com.dpw.ctms.move.service.impl.TripDataServiceImpl;
import com.dpw.ctms.move.service.impl.TripStopTaskServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TripStopTaskServiceImplTest {

    @Mock
    private TripDataServiceImpl tripDataService;

    @Mock
    private TaskMapper taskMapper;

    @InjectMocks
    private TripStopTaskServiceImpl tripStopTaskService;

    private String tripCode;
    private FilterRequest<TripTaskListingRequest> filterRequest;
    private Pagination pagination;
    private TripTaskListingRequest tripTaskListingRequest;

    private Stop stop1;
    private Stop stop2;
    private Stop stop3;
    private StopTask stopTask1;
    private StopTask stopTask2;
    private StopTask stopTask3;
    private Task task1;
    private Task task2;
    private Task task3;
    private TripTasksDetailsDTO taskDTO1;
    private TripTasksDetailsDTO taskDTO2;
    private TripTasksDetailsDTO taskDTO3;

    @BeforeEach
    void setUp() {
        tripCode = "TRIP001";

        // Setup pagination
        pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);

        // Setup request objects
        tripTaskListingRequest = new TripTaskListingRequest();
        filterRequest = new FilterRequest<>();
        filterRequest.setPagination(pagination);
        filterRequest.setFilters(tripTaskListingRequest);

        // Setup tasks
        task1 = new Task();
        task1.setId(1L);
        task1.setCode("TASK001");

        task2 = new Task();
        task2.setId(2L);
        task2.setCode("TASK002");

        task3 = new Task();
        task3.setId(3L);
        task3.setCode("TASK003");

        // Setup stop tasks
        stopTask1 = new StopTask();
        stopTask1.setId(1L);
        stopTask1.setTask(task1);

        stopTask2 = new StopTask();
        stopTask2.setId(2L);
        stopTask2.setTask(task2);

        stopTask3 = new StopTask();
        stopTask3.setId(3L);
        stopTask3.setTask(task3);

        // Setup stops with different sequences to test sorting
        stop1 = new Stop();
        stop1.setId(1L);
        stop1.setSequence(2);
        stop1.setExternalLocationCode("EXT001");
        stop1.setStopTasks(Arrays.asList(stopTask1, stopTask2));

        stop2 = new Stop();
        stop2.setId(2L);
        stop2.setSequence(1);
        stop2.setExternalLocationCode("EXT002");
        stop2.setStopTasks(Arrays.asList(stopTask3));

        stop3 = new Stop();
        stop3.setId(3L);
        stop3.setSequence(null); // Test null sequence
        stop3.setExternalLocationCode("EXT003");
        stop3.setStopTasks(new ArrayList<>()); // Empty task list

        // Setup task DTOs
        taskDTO1 = new TripTasksDetailsDTO();
        taskDTO1.setCode("TASK001");

        taskDTO2 = new TripTasksDetailsDTO();
        taskDTO2.setCode("TASK002");

        taskDTO3 = new TripTasksDetailsDTO();
        taskDTO3.setCode("TASK003");
    }

    @Test
    void getTripTasks_WithValidData_ShouldReturnSortedListResponse() {
        // Given
        Page<Stop> mockStops = new PageImpl<>(Arrays.asList(stop1, stop2, stop3));

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(mockStops);
        when(taskMapper.toDTO(task1)).thenReturn(taskDTO1);
        when(taskMapper.toDTO(task2)).thenReturn(taskDTO2);
        when(taskMapper.toDTO(task3)).thenReturn(taskDTO3);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(3, result.getData().size());

        // Verify sorting: stop3 (null sequence) should be first, then stop2 (sequence=1), then stop1 (sequence=2)
        TripFacilityTasksDetailsResponse firstResponse = result.getData().get(0);
        assertEquals("EXT003", firstResponse.getLocationDetails().getExternalLocationCode());
        assertTrue(firstResponse.getLocationTaskDetailsList().isEmpty());

        TripFacilityTasksDetailsResponse secondResponse = result.getData().get(1);
        assertEquals("EXT002", secondResponse.getLocationDetails().getExternalLocationCode());
        assertEquals(1, secondResponse.getLocationTaskDetailsList().size());
        assertEquals("TASK003", secondResponse.getLocationTaskDetailsList().get(0).getCode());

        TripFacilityTasksDetailsResponse thirdResponse = result.getData().get(2);
        assertEquals("EXT001", thirdResponse.getLocationDetails().getExternalLocationCode());
        assertEquals(2, thirdResponse.getLocationTaskDetailsList().size());

        // Verify service calls
        verify(tripDataService, times(1)).getTripStopByTripCode(tripCode, pagination);
        verify(taskMapper, times(1)).toDTO(task1);
        verify(taskMapper, times(1)).toDTO(task2);
        verify(taskMapper, times(1)).toDTO(task3);
    }

    @Test
    void getTripTasks_WithEmptyStopList_ShouldReturnEmptyListResponse() {
        // Given
        Page<Stop> emptyStops = new PageImpl<>(Collections.emptyList());

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(emptyStops);
        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalRecords());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());

        verify(tripDataService, times(1)).getTripStopByTripCode(tripCode, pagination);
    }

    @Test
    void getTripTasks_WithStopsHavingNoTasks_ShouldReturnResponsesWithEmptyTaskLists() {
        // Given
        Stop stopWithNoTasks = new Stop();
        stopWithNoTasks.setId(1L);
        stopWithNoTasks.setSequence(1);
        stopWithNoTasks.setExternalLocationCode("EXT001");
        stopWithNoTasks.setStopTasks(new ArrayList<>());

        Page<Stop> stops = new PageImpl<>(Arrays.asList(stopWithNoTasks));

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(stops);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getData().size());

        TripFacilityTasksDetailsResponse response = result.getData().get(0);
        assertEquals("EXT001", response.getLocationDetails().getExternalLocationCode());
        assertTrue(response.getLocationTaskDetailsList().isEmpty());

    }

    @Test
    void getTripTasks_WithNullSequences_ShouldHandleSortingCorrectly() {
        // Given
        Stop stopWithNullSequence1 = new Stop();
        stopWithNullSequence1.setId(1L);
        stopWithNullSequence1.setSequence(null);
        stopWithNullSequence1.setExternalLocationCode("EXT001");
        stopWithNullSequence1.setStopTasks(new ArrayList<>());

        Stop stopWithNullSequence2 = new Stop();
        stopWithNullSequence2.setId(2L);
        stopWithNullSequence2.setSequence(null);
        stopWithNullSequence2.setExternalLocationCode("EXT002");
        stopWithNullSequence2.setStopTasks(new ArrayList<>());

        Stop stopWithSequence = new Stop();
        stopWithSequence.setId(3L);
        stopWithSequence.setSequence(5);
        stopWithSequence.setExternalLocationCode("EXT003");
        stopWithSequence.setStopTasks(new ArrayList<>());

        Page<Stop> stops = new PageImpl<>(Arrays.asList(stopWithSequence, stopWithNullSequence1, stopWithNullSequence2));

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(stops);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(3, result.getData().size());

        // Null sequences should come first, then ordered sequences
        List<String> locationCodes = result.getData().stream()
                .map(r -> r.getLocationDetails().getExternalLocationCode())
                .toList();

        // First two should be the null sequence stops (order may vary)
        assertTrue(locationCodes.subList(0, 2).containsAll(Arrays.asList("EXT001", "EXT002")));
        // Last should be the stop with sequence 5
        assertEquals("EXT003", locationCodes.get(2));
    }

    @Test
    void getTripTasks_WithNullTripCode_ShouldPassNullToService() {
        // Given
        Page<Stop> expectedStopsPage = new PageImpl<>(Collections.emptyList());
        String nullTripCode = null;
        when(tripDataService.getTripStopByTripCode(nullTripCode, pagination))
                .thenReturn(expectedStopsPage);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(nullTripCode, filterRequest);

        // Then
        assertNotNull(result);
        verify(tripDataService, times(1)).getTripStopByTripCode(nullTripCode, pagination);
    }

    @Test
    void getTripTasks_WithSingleStopMultipleTasks_ShouldMapAllTasks() {
        // Given
        Page<Stop> stops = new PageImpl<>(Arrays.asList(stop1)); // stop1 has 2 tasks

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(stops);
        when(taskMapper.toDTO(task1)).thenReturn(taskDTO1);
        when(taskMapper.toDTO(task2)).thenReturn(taskDTO2);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getData().size());

        TripFacilityTasksDetailsResponse response = result.getData().get(0);
        assertEquals(2, response.getLocationTaskDetailsList().size());

        List<String> taskCodes = response.getLocationTaskDetailsList().stream()
                .map(TripTasksDetailsDTO::getCode)
                .toList();
        assertTrue(taskCodes.contains("TASK001"));
        assertTrue(taskCodes.contains("TASK002"));

        verify(taskMapper, times(1)).toDTO(task1);
        verify(taskMapper, times(1)).toDTO(task2);
    }

    @Test
    void getTripTasks_WhenTaskMapperReturnsNull_ShouldHandleGracefully() {
        // Given
        Page<Stop> stops = new PageImpl<>(Arrays.asList(stop2)); // stop2 has 1 task

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(stops);
        when(taskMapper.toDTO(task3)).thenReturn(null);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getData().size());

        TripFacilityTasksDetailsResponse response = result.getData().get(0);
        assertEquals(1, response.getLocationTaskDetailsList().size());
        assertNull(response.getLocationTaskDetailsList().get(0));

        verify(taskMapper, times(1)).toDTO(task3);
    }

    @Test
    void getTripTasks_WhenTripDataServiceThrowsException_ShouldPropagateException() {
        // Given
        RuntimeException expectedException = new RuntimeException("Service error");
        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenThrow(expectedException);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> tripStopTaskService.getTripTasks(tripCode, filterRequest));

        assertEquals("Service error", exception.getMessage());
        verify(tripDataService, times(1)).getTripStopByTripCode(tripCode, pagination);
        verify(tripDataService, never()).getTotalStopCount(anyString());
    }

    @Test
    void getTripTasks_WhenTaskMapperThrowsException_ShouldPropagateException() {
        // Given
        Page<Stop> stops = new PageImpl<>(Arrays.asList(stop1));

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(stops);
        RuntimeException expectedException = new RuntimeException("Mapping error");
        when(taskMapper.toDTO(task1)).thenThrow(expectedException);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> tripStopTaskService.getTripTasks(tripCode, filterRequest));

        assertEquals("Mapping error", exception.getMessage());
        verify(taskMapper, times(1)).toDTO(task1);
    }

    @Test
    void getTripTasks_ShouldBuildCorrectBasicDetailDTO() {
        // Given
        Stop stopWithSpecificCode = new Stop();
        stopWithSpecificCode.setId(1L);
        stopWithSpecificCode.setSequence(1);
        stopWithSpecificCode.setExternalLocationCode("SPECIFIC_CODE_123");
        stopWithSpecificCode.setStopTasks(new ArrayList<>());

        Page<Stop> stops = new PageImpl<>(Arrays.asList(stopWithSpecificCode));

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(stops);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getData().size());

        TripFacilityTasksDetailsResponse response = result.getData().get(0);
        LocationDetail locationDetail = response.getLocationDetails();
        assertNotNull(locationDetail);
        assertEquals("SPECIFIC_CODE_123", locationDetail.getExternalLocationCode());
    }

    @Test
    void getTripTasks_WithMixedSequenceOrdering_ShouldSortCorrectly() {
        // Given
        Stop stop1 = new Stop();
        stop1.setSequence(10);
        stop1.setExternalLocationCode("EXT010");
        stop1.setStopTasks(new ArrayList<>());

        Stop stop2 = new Stop();
        stop2.setSequence(5);
        stop2.setExternalLocationCode("EXT005");
        stop2.setStopTasks(new ArrayList<>());

        Stop stop3 = new Stop();
        stop3.setSequence(null);
        stop3.setExternalLocationCode("EXT_NULL");
        stop3.setStopTasks(new ArrayList<>());

        Stop stop4 = new Stop();
        stop4.setSequence(1);
        stop4.setExternalLocationCode("EXT001");
        stop4.setStopTasks(new ArrayList<>());

        Page<Stop> stops = new PageImpl<>(Arrays.asList(stop1, stop2, stop3, stop4));
        Long totalCount = 4L;

        when(tripDataService.getTripStopByTripCode(tripCode, pagination))
                .thenReturn(stops);

        // When
        ListResponse<TripFacilityTasksDetailsResponse> result =
                tripStopTaskService.getTripTasks(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(4, result.getData().size());

        List<String> orderedCodes = result.getData().stream()
                .map(r -> r.getLocationDetails().getExternalLocationCode())
                .toList();

        // Expected order: null first, then 1, 5, 10
        assertEquals("EXT_NULL", orderedCodes.get(0));
        assertEquals("EXT001", orderedCodes.get(1));
        assertEquals("EXT005", orderedCodes.get(2));
        assertEquals("EXT010", orderedCodes.get(3));
    }
}