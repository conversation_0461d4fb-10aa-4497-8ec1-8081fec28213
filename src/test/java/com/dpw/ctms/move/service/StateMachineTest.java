package com.dpw.ctms.move.service;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.helper.TaskOperationInDB;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.StateMachineConfigReader;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;

@AutoConfigureMockMvc
@ExtendWith(MockitoExtension.class)
public class StateMachineTest extends BaseTest {

    @MockBean
    private StateMachineConfigReader stateMachineConfigReader;

    @Autowired
    private TaskOperationInDB taskOperationInDB;

    @Autowired
    private StateMachineServiceRegistry stateMachineServiceRegistry;

    @Test
    public void exception_thrown_when_is_initial_is_not_set_for_any_states() {
        Mockito.when(stateMachineConfigReader.readStateMachineConfig())
                .thenReturn(Fakers.createStateMachineTenantConfigMapWithoutInitialState("CFR_RINKERS"));
        Task task = taskOperationInDB.createTask();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TASK);
        TMSException exception = Assertions.assertThrows(TMSException.class, () -> {
            stateMachineService.handleEvent("CFR_RINKERS", "MARK_TASK_COMPLETED", task.getId());
        });
        Assertions.assertTrue(exception.getErrorMessage().contains("Exception occurred while creating state machine"));
    }

    @Test
    public void change_task_status_from_CREATED_to_COMPLETED() {
        Mockito.when(stateMachineConfigReader.readStateMachineConfig())
                .thenReturn(Fakers.createStateMachineTenantConfigMap("CFR_RINKERS"));
        Task task = taskOperationInDB.createTask();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TASK);
        stateMachineService.handleEvent("CFR_RINKERS", "MARK_TASK_COMPLETED", task.getId());
        Assertions.assertEquals(TaskStatus.COMPLETED, taskOperationInDB.getTaskById(task.getId()).getStatus());
    }

    @Test
    public void change_task_status_from_CREATED_to_CLOSED() {
        Mockito.when(stateMachineConfigReader.readStateMachineConfig())
                .thenReturn(Fakers.createStateMachineTenantConfigMap("CFR_RINKERS"));
        Task task = taskOperationInDB.createTask();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TASK);
        stateMachineService.handleEvent("CFR_RINKERS", "COMPLETED_TASK_WITH_ATTRIBUTES", task.getId());
        Assertions.assertEquals(TaskStatus.CLOSED, taskOperationInDB.getTaskById(task.getId()).getStatus());
    }
}