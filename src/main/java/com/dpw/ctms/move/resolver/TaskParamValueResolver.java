package com.dpw.ctms.move.resolver;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.registry.TaskParamStrategyRegistry;
import com.dpw.ctms.move.strategy.taskparam.ITaskParamValueStrategy;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.PARAM_VALUE_VALIDATIONS_FAILED;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;


@Component
@RequiredArgsConstructor
public class TaskParamValueResolver {
    private final Validator validator;
    private final TaskParamStrategyRegistry taskParamStrategyRegistry;

    public ParamValueBaseDTO resolve(TaskParamType paramName, JsonNode paramValueJsonNode) {
        ITaskParamValueStrategy strategy = taskParamStrategyRegistry.getTaskParamValueStrategy(paramName);
        Class<? extends ParamValueBaseDTO> paramValue = strategy.getParamValue();
        ParamValueBaseDTO paramValueDTO = ObjectMapperUtil.getObjectMapper().convertValue(
                paramValueJsonNode,
                paramValue
        );
        Set<ConstraintViolation<ParamValueBaseDTO>> violations = validator.validate(paramValueDTO);
        if (!violations.isEmpty()) {
            String errorMessages = violations.stream()
                    .map(v -> v.getPropertyPath() + ": " + v.getMessage())
                    .collect(Collectors.joining(", "));
            throw new TMSException(INVALID_REQUEST.name(),
                    String.format(PARAM_VALUE_VALIDATIONS_FAILED, paramName, errorMessages));
        }
        return paramValueDTO;
    }
}
