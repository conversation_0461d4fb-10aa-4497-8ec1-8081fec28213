#!/bin/bash

# Variables
NAMESPACE=$1
DEPLOYMENT_NAME=$2
EXPECTED_IMAGE_TAG=$3
CONTAINER_NAME=$4
INITIAL_WAIT=120
STATUS_CHECK_INTERVAL=60
clusterName=$5
resourceGroupName=$6


# connect to the AKS k8s cluster
az aks get-credentials --name $clusterName --resource-group $resourceGroupName --context $clusterName --overwrite-existing

# set context to application namespace
kubectl config set-context --current --namespace="$NAMESPACE"

# Initial wait
echo "Waiting for $INITIAL_WAIT seconds before checking pod status..."
sleep "$INITIAL_WAIT"

# Function to check if all pods are running
check_pods_running() {
  POD_NAMES=$(kubectl get pods -n "$NAMESPACE" -l app="$DEPLOYMENT_NAME" -o=jsonpath='{.items[*].metadata.name}')
  
  if [ -z "$POD_NAMES" ]; then
    echo "No pods found for deployment '$DEPLOYMENT_NAME' in namespace '$NAMESPACE'."
    return 1
  fi

  for POD_NAME in $POD_NAMES; do
    POD_STATUS=$(kubectl get pod "$POD_NAME" -n "$NAMESPACE" -o=jsonpath='{.status.phase}')
    if [ "$POD_STATUS" != "Running" ]; then
      echo "Pod '$POD_NAME' is not running yet (current status: $POD_STATUS)."
      return 1
    fi
  done

  echo "All pods are in 'Running' state."
  return 0
}

# Function to check all pods' image tags
check_pods_image_tags() {
  POD_NAMES=$(kubectl get pods -n "$NAMESPACE" -l app="$DEPLOYMENT_NAME" -o=jsonpath='{.items[*].metadata.name}')
  
  for POD_NAME in $POD_NAMES; do
    CURRENT_IMAGE_TAG=$(kubectl get pod "$POD_NAME" -n "$NAMESPACE" -o=jsonpath="{.spec.containers[?(@.name=='$CONTAINER_NAME')].image}" | awk -F: '{print $2}')
    if [ "$CURRENT_IMAGE_TAG" != "$EXPECTED_IMAGE_TAG" ]; then
      echo "Image tag mismatch for pod '$POD_NAME'. Current: $CURRENT_IMAGE_TAG, Expected: $EXPECTED_IMAGE_TAG"
      return 1
    fi
  done

  echo "All pods have the expected image tag '$EXPECTED_IMAGE_TAG'."
  return 0
}

# Start checking pod status

for (( i=1 ; i<=7 ; i++ )); 
do
  if check_pods_running; then
    echo "Pods are running. Checking image tags..."
    if check_pods_image_tags; then
      echo "Validation successful: Pods are running with the correct image tag."
      exit 0
    fi
  fi

  echo "Retrying in $STATUS_CHECK_INTERVAL seconds..."
  sleep "$STATUS_CHECK_INTERVAL"
done

echo "Validation failed: Pods are not running with the expected image tag."
exit 1