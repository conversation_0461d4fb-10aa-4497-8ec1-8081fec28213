package com.dpw.ctms.move.validator;

import com.dpw.ctms.move.annotation.ValidEnum;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class EnumValidator implements ConstraintValidator<ValidEnum, String> {
    private List<String> acceptedValues;

    @Override
    public void initialize(ValidEnum annotation) {
        acceptedValues = Stream.of(annotation.enumClass().getEnumConstants())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        boolean isValid = acceptedValues.contains(value);

        if (!isValid) {
            /** Disable default message */
            context.disableDefaultConstraintViolation();

            /** Build a custom message with the enum values */
            String template = context.getDefaultConstraintMessageTemplate();
            String messageWithValues = template.replace("{enumValues}", String.join(", ", acceptedValues));

            context.buildConstraintViolationWithTemplate(messageWithValues)
                    .addConstraintViolation();
        }

        return isValid;
    }
}