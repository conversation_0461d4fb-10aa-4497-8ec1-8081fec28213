package com.dpw.ctms.move.enums;

public enum ShipmentStatus implements DisplayableStatusEnum  {
    ASSIGNED("Assigned"),
    ALLOCATED("Allocated"),
    IN_TRANSIT("In Transit"),
    DELIVERED("Delivered"),
    DELIVERED_WITH_EXCEPTION("Delivered With Exception"),
    CANCELLED("Cancelled");

    private final String displayText;

    ShipmentStatus (String displayText) {
        this.displayText = displayText;
    }

    public String getDisplayName() {
        return this.displayText;
    }

    public static ShipmentStatus fromDisplayName(String displayText) {
        if (displayText == null) {
            return null;
        }
        for (ShipmentStatus status : ShipmentStatus.values()) {
            if (status.displayText.equalsIgnoreCase(displayText)) {
                return status;
            }
        }
        return null;
    }
}
