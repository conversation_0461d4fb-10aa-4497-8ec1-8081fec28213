package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.response.TransportOrderResponse;

public interface ITransportOrderService {
    TransportOrderResponse createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest);
    TransportOrder saveTransportOrder(TransportOrder transportOrder);

}
