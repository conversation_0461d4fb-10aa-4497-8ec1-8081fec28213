package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.StopTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface StopRepository extends JpaRepository<Stop, Long> {
    Page<Stop> findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(String tripCode, List<String> statusList,
                                                                          String tripCode2, Pageable pageable);
    Long countDistinctByTrip_CodeAndStatusNotIn(String tripCode, List<String> statusList);
}
