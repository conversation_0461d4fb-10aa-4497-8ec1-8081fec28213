{"CFR_RINKERS": {"task": {"states": [{"state": "CREATED", "isInitial": "true"}, {"state": "COMPLETED", "isInitial": "false"}, {"state": "CLOSED", "isInitial": "false"}], "transitions": [{"sourceState": "CREATED", "targetState": "COMPLETED", "event": "MARK_TASK_COMPLETED", "actionId": "", "guardId": ""}, {"sourceState": "COMPLETED", "targetState": "CLOSED", "event": "MANDATORY_ATTRIBUTES_FILLED", "actionId": "", "guardId": "TASK_TO_CLOSED_GUARD_ID"}, {"sourceState": "CREATED", "targetState": "CLOSED", "event": "COMPLETED_TASK_WITH_ATTRIBUTES", "actionId": "", "guardId": "TASK_TO_CLOSED_GUARD_ID"}]}}}