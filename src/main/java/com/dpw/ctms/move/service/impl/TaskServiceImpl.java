package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_ID;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskServiceImpl implements ITaskService {

    private final TaskRepository taskRepository;

    @Override
    public Task findTaskById(Long taskId) {
        return taskRepository.findById(taskId).orElseThrow(() -> {
            log.error("Task id {} not found", taskId);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TASK_ID, taskId)
            );
        });
    }

    @Override
    public Task saveTask(Task task) {
        return taskRepository.save(task);
    }
}
