package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.tmsutils.context.TenantContext;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class ShipmentListingIntegrationTest extends IntegrationTestBase {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
    }

    @Test
    void listShipments_WithBasicRequest_ShouldReturnSuccessfully() throws Exception {
        // Arrange
        ShipmentListingRequest request = createBasicShipmentListingRequest();

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
        assertNotNull(response.getData());
    }

    @Test
    void listShipments_WithPagination_ShouldRespectPaginationLimits() throws Exception {
        // Arrange
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(5);
        
        ShipmentListingRequest request = createShipmentListingRequestWithPagination(pagination);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getData().size() <= 5);
    }

    @Test
    void listShipments_WithSorting_ShouldReturnSortedResults() throws Exception {
        // Arrange
        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");
        
        ShipmentListingRequest request = createShipmentListingRequestWithSort(sort);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        
        // Verify sorting if there are multiple results
        if (response.getData().size() > 1) {
            String firstCode = response.getData().get(0).getCode();
            String secondCode = response.getData().get(1).getCode();
            assertTrue(firstCode.compareTo(secondCode) <= 0, "Results should be sorted by code in ascending order");
        }
    }

    @Test
    void listShipments_WithShipmentStatusFilter_ShouldFilterCorrectly() throws Exception {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("ASSIGNED", "ALLOCATED"));
        ShipmentListingRequest request = createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        
        // Verify all returned shipments have the expected statuses
        response.getData().forEach(shipment -> {
            assertNotNull(shipment.getStatus());
            assertTrue(Arrays.asList("ASSIGNED", "ALLOCATED").contains(shipment.getStatus().getValue()),
                    "Shipment status should be ASSIGNED or ALLOCATED");
        });
    }

    @Test
    void listShipments_WithDateRangeFilter_ShouldFilterCorrectly() throws Exception {
        // Arrange
        long currentTime = System.currentTimeMillis();
        DateRange dateRange = new DateRange();
        dateRange.setFrom(currentTime - 86400000L); // 24 hours ago
        dateRange.setTo(currentTime + 86400000L);   // 24 hours from now
        
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setExpectedPickupDateRange(dateRange);
        ShipmentListingRequest request = createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithInvalidShipmentStatus_ShouldReturnEmptyResult() throws Exception {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("INVALID_STATUS"));
        ShipmentListingRequest request = createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords(), "Should find no shipments with invalid status");
        assertTrue(response.getData().isEmpty(), "Result list should be empty");
    }

    @Test
    void listShipments_WithEmptyFilter_ShouldReturnAllShipments() throws Exception {
        // Arrange
        ShipmentListingRequest.Filter emptyFilter = new ShipmentListingRequest.Filter();
        ShipmentListingRequest request = createShipmentListingRequestWithFilter(emptyFilter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithLargePageSize_ShouldLimitToMaxPageSize() throws Exception {
        // Arrange
        Pagination largePagination = new Pagination();
        largePagination.setPageNo(0);
        largePagination.setPageSize(1000); // Exceeds max of 500
        
        ShipmentListingRequest request = createShipmentListingRequestWithPagination(largePagination);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getData().size() <= 500, "Page size should be limited to 500");
    }

    @Test
    void listShipments_WithComplexFilter_ShouldExecuteMultipleSpecifications() throws Exception {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("ASSIGNED", "ALLOCATED"));
        
        long currentTime = System.currentTimeMillis();
        filter.setExpectedPickupDateRange(new DateRange(currentTime - 86400000L, currentTime + 86400000L));
        
        ShipmentListingRequest request = createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithNullRequestBody_ShouldReturnBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/v1/shipments/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("null"))
                .andExpect(status().isBadRequest());
    }

    // Helper methods
    private MvcResult performListShipmentsRequest(ShipmentListingRequest request) throws Exception {
        return mockMvc.perform(post("/v1/shipments/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();
    }

    private ListResponse<ShipmentListingResponse> parseResponse(MvcResult result) throws Exception {
        String content = result.getResponse().getContentAsString();
        return objectMapper.readValue(content, new TypeReference<ListResponse<ShipmentListingResponse>>() {});
    }

    private ShipmentListingRequest createBasicShipmentListingRequest() {
        ShipmentListingRequest request = new ShipmentListingRequest();
        request.setPagination(createDefaultPagination());
        request.setSort(createDefaultSort());
        request.setFilter(new ShipmentListingRequest.Filter());
        return request;
    }

    private ShipmentListingRequest createShipmentListingRequestWithPagination(Pagination pagination) {
        ShipmentListingRequest request = new ShipmentListingRequest();
        request.setPagination(pagination);
        request.setSort(createDefaultSort());
        request.setFilter(new ShipmentListingRequest.Filter());
        return request;
    }

    private ShipmentListingRequest createShipmentListingRequestWithSort(Sort sort) {
        ShipmentListingRequest request = new ShipmentListingRequest();
        request.setPagination(createDefaultPagination());
        request.setSort(sort);
        request.setFilter(new ShipmentListingRequest.Filter());
        return request;
    }

    private ShipmentListingRequest createShipmentListingRequestWithFilter(ShipmentListingRequest.Filter filter) {
        ShipmentListingRequest request = new ShipmentListingRequest();
        request.setPagination(createDefaultPagination());
        request.setSort(createDefaultSort());
        request.setFilter(filter);
        return request;
    }

    private Pagination createDefaultPagination() {
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);
        return pagination;
    }

    private Sort createDefaultSort() {
        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");
        return sort;
    }
}
