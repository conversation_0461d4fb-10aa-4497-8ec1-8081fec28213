package com.dpw.ctms.move.request;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VehicleOperatorResourceRequest{
        @NotBlank(message = "Vehicle operator code cannot be null")
        String code;

        @NotBlank(message = "Vehicle operator resource id cannot be null")
        String externalResourceId;

        @Valid
        ResourceAssignmentDetailsRequest resourceAssignmentDetails;

        @Valid
        List<AssignedStopRangeRequest> assignedStopRanges;

        JsonNode details;
}
