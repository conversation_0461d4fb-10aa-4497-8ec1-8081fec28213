package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.registry.EntityTypeEnumRegistry;
import com.dpw.ctms.move.request.StaticDataRequest;
import com.dpw.ctms.move.response.EntityStaticDataResponse;
import com.dpw.ctms.move.response.StaticDataResponse;
import com.dpw.ctms.move.service.IStaticService;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaticServiceImpl implements IStaticService {

    /**
     * This method processes each entity type individually, collecting successful results
     * and error details for any invalid entity types.
     *
     * @param request the request containing list of entity types to process
     * @return StaticDataResponse containing data for all valid entity types and error details for invalid ones
     */
    @Override
    public StaticDataResponse getStaticData(StaticDataRequest request) {
        log.info("Retrieving static data for {} entity types", request.getEntityTypes().size());

        Map<String, EntityStaticDataResponse> dataMap = new HashMap<>();
        List<String> successfulEntityTypes = new ArrayList<>();
        List<StaticDataResponse.EntityTypeError> failedEntityTypes = new ArrayList<>();

        // Process each entity type
        for (String entityType : request.getEntityTypes()) {
            try {
                EntityStaticDataResponse staticData = processEntityType(entityType);
                dataMap.put(staticData.getEntityType(), staticData);
                successfulEntityTypes.add(staticData.getEntityType());
                log.info("Successfully processed entity type: {}", entityType);
            } catch (TMSException e) {
                StaticDataResponse.EntityTypeError error = StaticDataResponse.EntityTypeError.builder()
                        .entityType(entityType)
                        .errorCode(e.getErrorCode())
                        .errorMessage(e.getErrorMessage())
                        .build();
                failedEntityTypes.add(error);
                log.error("Failed to process entity type: {} - {}", entityType, e.getErrorMessage());
            } catch (Exception e) {
                StaticDataResponse.EntityTypeError error = StaticDataResponse.EntityTypeError.builder()
                        .entityType(entityType)
                        .errorCode("INTERNAL_ERROR")
                        .errorMessage("Unexpected error processing entity type: " + e.getMessage())
                        .build();
                failedEntityTypes.add(error);
                log.error("Unexpected error processing entity type: {}", entityType, e);
            }
        }

        log.info("Processed {} entity types: {} successful, {} failed",
                request.getEntityTypes().size(), successfulEntityTypes.size(), failedEntityTypes.size());

        return StaticDataResponse.builder()
                .data(dataMap)
                .successfulEntityTypes(successfulEntityTypes)
                .failedEntityTypes(failedEntityTypes)
                .build();
    }

    /**
     * Processes a single entity type and returns its static data.
     *
     * @param entityType the entity type identifier
     * @return EntityStaticDataResponse containing the enum values
     * @throws TMSException if the entity type is not supported
     */
    private EntityStaticDataResponse processEntityType(String entityType) {
        log.info("Processing entity type: {}", entityType);

        // Validate entity type
        if (entityType.isBlank()) {
            log.error("Entity type cannot be null or empty");
            throw new TMSException(INVALID_REQUEST.name(), "Entity type cannot be null or empty");
        }

        String normalizedEntityType = entityType.toUpperCase();

        // Check if entity type is supported
        if (!EntityTypeEnumRegistry.isSupported(normalizedEntityType)) {
            String errorMessage = String.format("Unsupported entity type: %s. Supported types: %s",
                    entityType, EntityTypeEnumRegistry.getSupportedEntityTypes());
            log.error(errorMessage);
            throw new TMSException(INVALID_REQUEST.name(), errorMessage);
        }

        // Get enum class for the entity type
        Class<? extends Enum<?>> enumClass = EntityTypeEnumRegistry.getEnumClass(normalizedEntityType);

        // Convert enum values to StatusOption list
        List<EntityStaticDataResponse.StatusOption> options = convertEnumToStatusOptions(enumClass);

        log.info("Successfully processed entity type: {} with {} options", entityType, options.size());

        return EntityStaticDataResponse.builder()
                .entityType(normalizedEntityType)
                .options(options)
                .build();
    }

    private List<EntityStaticDataResponse.StatusOption> convertEnumToStatusOptions(Class<? extends Enum<?>> enumClass) {
        Enum<?>[] enumConstants = enumClass.getEnumConstants();

        return Arrays.stream(enumConstants)
                .map(this::createStatusOption)
                .collect(Collectors.toList());
    }

    /**
     * Creates a StatusOption from an enum constant.
     * Uses custom display name if the enum implements DisplayableStatusEnum,
     * otherwise uses the enum name formatted as a readable label.
     *
     * @param enumConstant the enum constant
     * @return StatusOption with appropriate label and value
     */
    private EntityStaticDataResponse.StatusOption createStatusOption(Enum<?> enumConstant) {
        String label = enumConstant.name();
        String value = enumConstant.name();

        if (enumConstant instanceof DisplayableStatusEnum displayableEnum) {
            label = displayableEnum.getDisplayName();
        }

        return EntityStaticDataResponse.StatusOption.builder()
                .label(label)
                .value(value)
                .build();
    }
}
