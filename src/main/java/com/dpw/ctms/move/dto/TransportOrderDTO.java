package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransportOrderDTO {
    private String code;
    private TransportOrderStatus status;
    private AssignmentType assignmentType;
    private String assignmentCode;
    private String assigneeIdentifier;
    private List<TripDTO> trips;
    private List<ShipmentDTO> shipments;
}
