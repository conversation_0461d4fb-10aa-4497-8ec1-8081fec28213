package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.controller.IntegrationTestBase;
import com.dpw.ctms.move.dto.ShipmentDTO;
import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.dto.TripDTO;
import com.dpw.ctms.move.dto.VehicleResourceDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertSame;


@SpringBootTest
class TransportOrderMapperTest extends IntegrationTestBase {

    @Autowired
    private TransportOrderMapper mapper;

    @Test
    void testToEntity_setsBackReferences_correctly() {
        // Given
        TransportOrderDTO dto = new TransportOrderDTO();

        TripDTO tripDTO = new TripDTO();
        ShipmentDTO shipmentDTO = new ShipmentDTO();

        dto.setTrips(List.of(tripDTO));
        dto.setShipments(List.of(shipmentDTO));

        VehicleResourceDTO vehicleResourceDTO = new VehicleResourceDTO();
        tripDTO.setVehicleResource(vehicleResourceDTO);


        // When
        TransportOrder entity = mapper.toEntity(dto);

        // Then
        Assertions.assertNotNull(entity);
        Assertions.assertNotNull(entity.getTrips());
        Assertions.assertNotNull(entity.getShipments());

        Assertions.assertEquals(1, entity.getTrips().size());
        Assertions.assertEquals(1, entity.getShipments().size());

        Trip mappedTrip = entity.getTrips().get(0);
        Shipment mappedShipment = entity.getShipments().get(0);

        assertSame(entity, mappedTrip.getTransportOrder(), "Trip should have back-reference to TransportOrder");
        assertSame(entity, mappedShipment.getTransportOrder(), "Shipment should have back-reference to TransportOrder");
    }
}
