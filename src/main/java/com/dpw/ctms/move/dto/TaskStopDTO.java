package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.StopTaskEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class TaskStopDTO extends StopDetailsDTO{
    private StopTaskEvent taskEvent;
}
