package com.dpw.ctms.move.registry;

import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.strategy.taskparam.ITaskParamValueStrategy;
import com.dpw.tmsutils.exception.TMSException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_PARAM_STRATEGY;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Component
public class TaskParamStrategyRegistry {
    private final Map<TaskParamType, ITaskParamValueStrategy> taskParamValueStrategiesMap = new HashMap<>();

    public TaskParamStrategyRegistry(List<ITaskParamValueStrategy> strategies) {
        for (ITaskParamValueStrategy strategy: strategies) {
            taskParamValueStrategiesMap.put(
                    strategy.getParamType(),
                    strategy
            );
        }
    }
    public ITaskParamValueStrategy getTaskParamValueStrategy(TaskParamType paramName) {
        ITaskParamValueStrategy strategy = taskParamValueStrategiesMap.get(paramName);
        if (strategy == null){
            throw new TMSException(
                    INVALID_REQUEST.name(),
                    String.format(INVALID_TASK_PARAM_STRATEGY, paramName)
            );
        }
        return strategy;
    }
}
