package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ShipmentFilteringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShipmentServiceImpl implements IShipmentService {

    private final ShipmentFilteringService shipmentFilteringService;

    @Override
    public ListResponse<ShipmentListingResponse> listShipments(ShipmentListingRequest shipmentListingRequest) {
        return shipmentFilteringService.filterShipments(shipmentListingRequest);
    }
}
