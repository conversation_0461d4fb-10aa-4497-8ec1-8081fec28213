trigger:
  branches:
    include:
    - Release-v*

resources:
  repositories:
  - repository: baseline_project_repo
    name: DevOps/_git/devops-common-utils
    type: git
    ref: main

variables:
  - group: ctms-move
  - group: TMS
  - group: tms_cluster_details
  - name: repo_name
    value: ctms-move
  - name: GRADLE_USER_HOME
    value: $(Pipeline.Workspace)/.gradle

pool:
  vmImage: 'ubuntu-latest'

name: $(Build.SourceBranchName)_$(Rev:r)

stages:
- stage: Build
  jobs:
  - job: BuildOnReleaseBranch
    displayName: 'Build on Release branch'
    steps:
      - task: Bash@3
        displayName: 'Check Branchname syntax and create BuildID'
        inputs:
          targetType: 'inline'
          script: |
            echo "Release version is --> $(Build.SourceBranchName)"
            if [[ $(Build.SourceBranchName) =~ ^Release-v[0-9][0-9]?\.[0-9][0-9]?\.[0-9][0-9]?$ ]]; then
              echo "Proper syntax for Release branch"
            else
              echo "Release branch syntax is incorrect. Please version properly"
              exit 1
            fi
            buildID=$(Build.BuildNumber)
            echo "##vso[task.setvariable variable=buildID;]$buildID"
            echo "Build ID is --> $buildID"

      - task: Bash@3
        displayName: 'Check Patch type from Release branch'
        inputs:
          targetType: 'inline'
          script: |
            repo_name=$(repo_name)
            main_branch=$(Build.SourceBranchName)
            commit_Id=$(Build.SourceVersion)
            echo $AccessToken | az devops login
            pr_source_branch=$(az repos pr list --include-links \
                     --org https://dpwhotfsonline.visualstudio.com/ \
                     --project TMS \
                     --repository ${repo_name} \
                     --status completed \
                     --target-branch ${main_branch} \
                     --query "[?lastMergeCommit.commitId==\`$commit_Id\`].{branchName:sourceRefName}" | grep -w 'branchName' | cut -d'"' -f4)
            hf_version_new=`echo $(Build.SourceBranchName) | cut -d"-" -f2 | cut -d"." -f3`
            if [ $hf_version_new -eq 0 ]; then
              echo "*****Commit has come from feature branch. This is Planned release*****"
              patchType="PL"
            elif [ $hf_version_new -ne 0 ]; then
              if [[ $pr_source_branch =~ ^refs/heads/feature/* ]]; then
                echo "*****Commit has come from feature branch. This is HF release*****"
                patchType="HF"
              elif [[ $pr_source_branch =~ ^refs/heads/hotfix/* ]]; then
                echo "*****Commit has come from hotfix branch. This is HF release*****"
                patchType="HF"
              elif [[ $pr_source_branch =~ ^refs/heads/BR/* ]]; then
                echo "*****Commit has come from BR branch. This is BR release*****"
                patchType="BR"
              else
                echo "*****Commit has come neither from BR or HF branch. It has to be either of those as this is Patch Release branch*****"
                exit 1
              fi
            fi
            echo "##vso[task.setvariable variable=patchType;]$patchType"
        env:
          AccessToken: $(System.AccessToken)

      - task: Bash@3
        displayName: 'Create Tag on commit'
        inputs:
          targetType: 'filePath'
          filePath: DevOps_Scripts/create_tag.sh
          arguments: '$(System.AccessToken) $(Build.SourceVersion) $(buildID) $(repo_name)'

      - task: Bash@3
        displayName: Update Build ID in variable group
        inputs:
          targetType: 'inline'
          script: |
            echo $AccessToken | az devops login
            az pipelines variable-group variable update --group-id  605 --name buildID --org https://dpwhotfsonline.visualstudio.com/ --project TMS --value $(buildID)
            az pipelines variable-group variable update --group-id  605 --name patchType --org https://dpwhotfsonline.visualstudio.com/ --project TMS --value $(patchType)
        env:
          AccessToken: $(System.AccessToken)

      - script: mkdir -p $(GRADLE_USER_HOME)
        displayName: 'Ensure Gradle Directory Exists'

      - task: Cache@2
        inputs:
          key: 'gradle | "$(Agent.OS)" | **/build.gradle.kts'
          restoreKeys: |
            gradle | "$(Agent.OS)"
            gradle
          path:  $(GRADLE_USER_HOME)
        displayName: 'Configure Gradle Caching'

      - task: Gradle@2
        inputs:
          workingDirectory: '$(System.DefaultWorkingDirectory)'
          gradleWrapperFile: 'gradlew'
          gradleOptions: '-Xmx3072m'
          publishJUnitResults: false
          tasks: 'clean build -x test'
          options: '--build-cache'
          javaHomeOption: 'JDKVersion'
          jdkVersionOption: '1.21'
          wrapperScript: 'gradlew'
          
      # - task: Veracode@3
      #   inputs:
      #     ConnectionDetailsSelection: 'Endpoint'
      #     AnalysisService: 'VeracodeConnection'
      #     veracodeAppProfile: 'ctms-move'
      #     version: '$(Build.BuildId)'
      #     filepath: '$(System.DefaultWorkingDirectory)/build/libs/ctms-move-0.0.1-SNAPSHOT.jar'
      #     failBuildOnPolicyFail: false
      #     maximumWaitTime: '360'

      - task: Docker@2
        displayName: Build & Push docker file
        inputs:
          containerRegistry: 'acrcargoestmsnonprod'
          repository: 'ctms-move'
          arguments: '--build-arg SYSTEM_ACCESSTOKEN=$(artifactory-token)'
          command: 'buildAndPush'
          Dockerfile: 'Dockerfile'
          tags: |
            $(buildID)
            latest
  
- stage: DeployQA
  dependsOn: Build
  jobs:
  - deployment: CreatePROnQA
    displayName: 'Create PR to QA'
    environment: 'Qa Approval'
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
              path: s
              displayName: 'Checking out my_current_repo'

            - script: |
                python -m pip install --upgrade pip
                pip install -r DevOps_Scripts/requirements.txt
              displayName: 'Install Python Packages from requirements.txt'


            - task: PythonScript@0
              displayName: 'Create PR to manifest repo for release branch'
              inputs:
                scriptSource: 'filePath'
                scriptPath: DevOps_Scripts/create_pr.py
                arguments: $(System.AccessToken) "tms-stack" "qa" $(Build.BuildNumber) "ctms-move"
                    
            - script: ./gradlew --stop    
              displayName: 'Stop Gradle Daemon'

- stage: DeployStaging
  dependsOn: Build
  jobs:
  - deployment: CreatePROnStaging
    displayName: 'Create PR to Staging'
    environment: 'Staging Approval'
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
              path: s
              displayName: 'Checking out my_current_repo'

            - script: |
                python -m pip install --upgrade pip
                pip install -r DevOps_Scripts/requirements.txt
              displayName: 'Install Python Packages from requirements.txt'

            - task: PythonScript@0
              displayName: 'Create PR to manifest repo for release branch'
              inputs:
                scriptSource: 'filePath'
                scriptPath: DevOps_Scripts/create_pr.py
                arguments: $(System.AccessToken) "tms-stack" "staging" $(Build.BuildNumber) "ctms-move"
            
            - task: Bash@3
              displayName: Trigger Veracode Policy Scan
              inputs:
                targetType: inline
                script: |-
                  echo $AccessToken | az devops login
                  az pipelines build queue --branch feature/policy-scan-veracode-changes --definition-name veracode-policy-scan-gradle --org "https://dpwhotfsonline.visualstudio.com/" --project TMS --variables "agent_name=tms-self-hosted-agent" "branch=$(Build.BuildNumber)" "repo_name=ctms-move" "java_version=1.17" "jar_path=ctms-move-0.0.1-SNAPSHOT.jar"
              env:
                AccessToken: $(System.AccessToken)

- stage: DeployPT
  dependsOn: Build
  jobs:
  - deployment: CreatePROnPT
    displayName: 'Create PR to PT'
    environment: 'Staging Approval'
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
              path: s
              displayName: 'Checking out my_current_repo'

            - script: |
                python -m pip install --upgrade pip
                pip install -r DevOps_Scripts/requirements.txt
              displayName: 'Install Python Packages from requirements.txt'


            - task: PythonScript@0
              displayName: 'Create PR to manifest repo for release branch'
              inputs:
                scriptSource: 'filePath'
                scriptPath: DevOps_Scripts/create_pr.py
                arguments: $(System.AccessToken) "tms-stack" "pt" $(Build.BuildNumber) "ctms-move"
           

- stage: DeployProd
  dependsOn: DeployStaging
  jobs:
  - deployment: CreatePROnProd
    displayName: 'Create PR to Prod'
    environment: 'Prod Approval'
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
              path: s
              displayName: 'Checking out my_current_repo'

            - script: |
                python -m pip install --upgrade pip
                pip install -r DevOps_Scripts/requirements.txt
              displayName: 'Install Python Packages from requirements.txt'


            - task: PythonScript@0
              displayName: 'Create PR to manifest repo for release branch'
              inputs:
                scriptSource: 'filePath'
                scriptPath: DevOps_Scripts/create_pr.py
                arguments: $(System.AccessToken) "tms-stack" "prod" $(Build.BuildNumber) "ctms-move"

            - checkout: baseline_project_repo
              displayName: Cheking baseline_project_repo

            - task: PythonScript@0
              displayName: 'Newrelic Deployment Marker'
              inputs:
                scriptSource: 'filePath'
                scriptPath: $(Build.SourcesDirectory)/devops-common-utils/jobs/newrelic_deployment_marker.py
                arguments: ctms-move-prod "$(Build.RequestedFor)" tms OTHER "$(Build.BuildNumber)" "$(Build.SourceVersionMessage)" "$(Build.SourceVersion)" "$(Build.SourceBranch)"

              
- stage: PostProdDeploy
  dependsOn: DeployProd
  jobs:
  - deployment: PostProdDeploy
    displayName: 'Post Prod Deploy'
    environment: 'Prod Approval'
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
              path: s
              displayName: 'Checking out my_current_repo'

            - task: Bash@3
              displayName: 'Auto Merge To Master Branch'
              inputs:
                targetType: filePath
                filePath: DevOps_Scripts/auto_merge_pr.sh
                arguments: $(System.AccessToken) $(repo_name) $(Build.BuildNumber)

            - task: Bash@3
              displayName: 'Locking Release Branch'
              inputs:
                targetType: 'inline'
                script: |
                  # Check if the branch is locked
                  LOCKED=$(az repos ref list --org "https://dpwhotfsonline.visualstudio.com/" --project TMS --repository $(repo_name) --filter "heads/$(Build.SourceBranchName)" --query "[0].isLocked")

                  if [[ $LOCKED == true ]]; then
                    echo "Branch is already locked. Skipping..."
                  else
                    echo "Locking branch"
                    az repos ref lock --name "heads/$(Build.SourceBranchName)" --org "https://dpwhotfsonline.visualstudio.com/" --project TMS --repository $(repo_name)
                  fi

            - task: Bash@3
              displayName: 'Get Next Release Branch Version'
              inputs:
                targetType: filePath
                filePath: DevOps_Scripts/get_next_release.sh
                arguments: $(Build.BuildNumber)
            
            - task: Bash@3
              displayName: Trigger Create Release Branch Pipeline
              inputs:
                targetType: inline
                script: |-
                  echo $AccessToken | az devops login
                  response=$(az pipelines build queue --branch $(Build.SourceBranchName) --definition-name $(create_branch_pipeline) --org https://dpwhotfsonline.visualstudio.com/ --project TMS --variables Current_Deployed_Release=$(current_release) Release_Branch=$(next_Release) --output json)
                  buildId=$(echo $response | jq -r '.id')
                  echo "Triggered build with ID: $buildId"
                  echo "Waiting for the pipeline to complete..."
                  while true; do
                    status=$(az pipelines build show --id $buildId --org https://dpwhotfsonline.visualstudio.com/ --project TMS --output json | jq -r '.status')
                    if [ "$status" == "completed" ]; then
                        result=$(az pipelines build show --id $buildId --org https://dpwhotfsonline.visualstudio.com/ --project TMS --output json | jq -r '.result')
                        if [ "$result" == "succeeded" ]; then
                            echo "Pipeline completed successfully"
                            break
                        else
                            echo "Pipeline completed with status - $result"
                            exit 1
                        fi
                    elif [ "$status" == "inProgress" ]; then
                        echo "Pipeline is still in progress..."
                    elif [ "$status" == "notStarted" ]; then
                        echo "Pipeline is not started..."
                    else
                        echo "Pipeline failed or was canceled"
                        exit 1
                    fi
                    sleep 30
                  done
              env:
                AccessToken: $(System.AccessToken)
              condition: eq(variables['flag'], '0')
