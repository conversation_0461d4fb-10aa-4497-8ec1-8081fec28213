trigger: none
 
pool:
   vmImage: 'ubuntu-latest'

name: $(Rev:r)

steps:
  - task: Bash@3
    displayName: 'Release version Validation'
    inputs:
      targetType: 'filePath'
      filePath: DevOps_Scripts/branch_name_validation.sh
      arguments: $(Release_Branch) $(Current_Deployed_Release)

  - task: PythonScript@0
    displayName: 'Create Release Branch with given Release version'
    inputs:
      scriptSource: 'filePath'
      scriptPath: DevOps_Scripts/create_branch.py
      arguments: $(Repo) $(Main_Branch) $(Release_Branch) 
    env:
      AccessToken: $(System.AccessToken)

  - task: Bash@3
    displayName: 'Set Policies on new Release branch'
    inputs:
      targetType: 'filePath'
      filePath: DevOps_Scripts/set_branch_policies.sh
      arguments: $(repo_Id) $(Release_Branch)
    env:
      AccessToken: $(System.AccessToken)
  
  - task: Bash@3
    displayName: Trigger Sonar Build pipeline
    inputs:
      targetType: 'inline'
      script: |
        echo $AccessToken | az devops login
        az pipelines build queue --definition-name $(sonar_name) --org https://dpwhotfsonline.visualstudio.com/ --project TMS --branch $(Release_Branch)
    env:
      AccessToken: $(System.AccessToken)
