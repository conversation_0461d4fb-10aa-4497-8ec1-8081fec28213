#! /bin/bash

buildID=$1
hotfix_number=`echo ${buildID}| cut -d'.' -f3 | cut -d'_' -f1`

current_release=""
next_Release=""
flag=""

if [[ $hotfix_number -ne 0 ]]; then
    echo "*****This is a Hotfix Release. Skipping Creating Release Branch*****"
    
    echo "##vso[task.setvariable variable=flag;isSecret=false;isOutput=true;]1"
else
    echo "*****This is a Normal Release. Running Creating Release Branch pipeline*****"
    current_release=`echo ${buildID} | cut -d'_' -f1`
    major_version=`echo ${buildID} | cut -d"-" -f2 | cut -d"." -f1 | cut -c2`
    minor_version=`echo ${buildID} | cut -d"." -f2`
    minor_version=$((minor_version+1))
    next_Release="Release-v$major_version.$minor_version.0"
    echo "This is the current release version - $current_release"
    echo "This is the next release version - $next_Release"
    echo "##vso[task.setvariable variable=current_release;]$current_release"
    echo "##vso[task.setvariable variable=next_Release;]$next_Release"
    echo "##vso[task.setvariable variable=flag;]0"
fi