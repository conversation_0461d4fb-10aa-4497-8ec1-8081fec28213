package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentListingResponse {
    private String code;

    private String tripCode;

    private String customerOrderId;

    private String consignmentId;

    private String transportOrderCode;

    private EnumLabelValueResponse status;

    private Long updatedAt;

    private TimeRange expectedTimes;

    private TimeRange actualTimes;

    private Stop originStop;

    private Stop destinationStop;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Stop {
        private String id;
        private String code;
        private String externalLocationCode;
        private EnumLabelValueResponse status;
        private Long updatedAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TimeRange {
        private Long startAt;
        private Long endAt;
    }
}
