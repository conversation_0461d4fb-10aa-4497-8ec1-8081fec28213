package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.service.ITaskService;
import org.springframework.stereotype.Service;

@Service
public class TaskStateMachineServiceImpl extends AbstractStateMachineService<Task> {

    private final ITaskService taskService;

    protected TaskStateMachineServiceImpl(StateMachineFactoryService stateMachineFactoryService, ITaskService taskService) {
        super(stateMachineFactoryService);
        this.taskService = taskService;
    }

    @Override
    protected StateMachineEntityType getStateMachineEntityType() {
        return StateMachineEntityType.TASK;
    }

    @Override
    protected Task findEntityById(Long id) {
        return taskService.findTaskById(id);
    }

    @Override
    protected String extractState(Task task) {
        return task.getStatus().name();
    }

    @Override
    protected void updateState(Task task, String newState) {
        task.setStatus(TaskStatus.valueOf(newState));
    }

    @Override
    protected void saveEntity(Task task) {
        taskService.saveTask(task);
    }
}
