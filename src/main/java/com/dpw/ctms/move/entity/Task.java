package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.TaskStatus;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.List;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "task")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Task extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private TaskStatus status;

    @Column(name = "sequence")
    private Integer sequence;

    @Column(name = "expected_start_at")
    private Long expectedStartAt;

    @Column(name = "expected_end_at")
    private Long expectedEndAt;

    @Column(name = "actual_start_at")
    private Long actualStartAt;

    @Column(name = "actual_end_at")
    private Long actualEndAt;

    private String externalTaskRegistrationCode;

    @Column(name = "external_task_master_code")
    private String externalTaskMasterCode;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;

    @OneToOne(mappedBy = "task")
    private StopTask stopTask;

    @OneToOne(mappedBy = "task")
    private ShipmentTask shipmentTask;

    @OneToMany(mappedBy = "task", cascade = CascadeType.ALL)
    private List<TaskParam> taskParams;

    @OneToMany(mappedBy = "task", cascade = CascadeType.ALL)
    private List<ExceptionTaskMapping> exceptionTaskMappings;
}