package com.dpw.ctms.move.request;

import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class DateRangeTest {

    private final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    private final Validator validator = factory.getValidator();

    @Test
    void dateRange_WithValidRange_ShouldPassValidation() {
        // Arrange
        DateRange dateRange = DateRange.builder()
                .from(1000L)
                .to(2000L)
                .build();

        // Act
        Set<ConstraintViolation<DateRange>> violations = validator.validate(dateRange);

        // Assert
        assertTrue(violations.isEmpty());
    }

    @Test
    void dateRange_WithInvalidRange_ShouldFailValidation() {
        // Arrange
        DateRange dateRange = DateRange.builder()
                .from(2000L)
                .to(1000L)
                .build();

        // Act
        Set<ConstraintViolation<DateRange>> violations = validator.validate(dateRange);

        // Assert
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertTrue(violations.iterator().next().getMessage().contains("From date must be less than or equal to To date"));
    }

    @Test
    void dateRange_WithOnlyFromDate_ShouldPassValidation() {
        // Arrange
        DateRange dateRange = DateRange.builder()
                .from(1000L)
                .build();

        // Act
        Set<ConstraintViolation<DateRange>> violations = validator.validate(dateRange);

        // Assert
        assertTrue(violations.isEmpty());
    }

    @Test
    void dateRange_WithOnlyToDate_ShouldPassValidation() {
        // Arrange
        DateRange dateRange = DateRange.builder()
                .to(2000L)
                .build();

        // Act
        Set<ConstraintViolation<DateRange>> violations = validator.validate(dateRange);

        // Assert
        assertTrue(violations.isEmpty());
    }

    @Test
    void dateRange_WithNullValues_ShouldPassValidation() {
        // Arrange
        DateRange dateRange = new DateRange();

        // Act
        Set<ConstraintViolation<DateRange>> violations = validator.validate(dateRange);

        // Assert
        assertTrue(violations.isEmpty());
    }

    @Test
    void dateRange_WithEqualFromAndTo_ShouldPassValidation() {
        // Arrange
        DateRange dateRange = DateRange.builder()
                .from(1000L)
                .to(1000L)
                .build();

        // Act
        Set<ConstraintViolation<DateRange>> violations = validator.validate(dateRange);

        // Assert
        assertTrue(violations.isEmpty());
    }
}
