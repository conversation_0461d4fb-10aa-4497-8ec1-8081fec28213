package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.TaskStatus;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDTO {
    private String code;
    private Integer sequence;
    private TaskStatus status;
    private Long expectedStartAt;
    private Long expectedEndAt;
    private List<TaskParamDTO> taskParams;
    private String externalTaskMasterCode;
    private JsonNode details;
}
